<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حساب البقرة</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; }
        input, select { width: 200px; padding: 5px; }
        button { padding: 10px 20px; margin: 10px 0; }
        .results { margin-top: 20px; padding: 10px; border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>اختبار حساب احتياجات البقرة الحلوب</h1>
    
    <div class="form-group">
        <label>وزن البقرة (كغ):</label>
        <input type="number" id="cowWeight" value="550" min="350" max="800">
    </div>
    
    <div class="form-group">
        <label>إنتاج الحليب (لتر/يوم):</label>
        <input type="number" id="cowMilk" value="25" min="0" max="80">
    </div>
    
    <div class="form-group">
        <label>مرحلة الإنتاج:</label>
        <select id="cowStage">
            <option value="early">مبكرة (0-100 يوم)</option>
            <option value="mid" selected>متوسطة (100-200 يوم)</option>
            <option value="late">متأخرة (200-305 يوم)</option>
            <option value="dry">جافة</option>
        </select>
    </div>
    
    <div class="form-group">
        <label>مستوى النشاط:</label>
        <select id="activityLevel">
            <option value="low">منخفض</option>
            <option value="moderate" selected>متوسط</option>
            <option value="high">عالي</option>
        </select>
    </div>
    
    <button onclick="testCalculation()">احسب الاحتياجات</button>
    
    <div id="results" class="results" style="display: none;">
        <h3>النتائج:</h3>
        <div id="resultsContent"></div>
    </div>

    <script>
        function calculateNutritionalRequirements(weight, milk, stage, activity) {
            // معاملات التصحيح حسب مرحلة الإنتاج
            const stageFactors = {
                'early': { dm: 1.1, protein: 1.15, energy: 1.1 },
                'mid': { dm: 1.0, protein: 1.0, energy: 1.0 },
                'late': { dm: 0.95, protein: 0.9, energy: 0.95 },
                'dry': { dm: 0.8, protein: 0.7, energy: 0.8 }
            };
            
            // معاملات التصحيح حسب مستوى النشاط
            const activityFactors = {
                'low': { dm: 1.0, energy: 1.0 },
                'moderate': { dm: 1.05, energy: 1.1 },
                'high': { dm: 1.1, energy: 1.2 }
            };
            
            const stageFactor = stageFactors[stage];
            const activityFactor = activityFactors[activity];
            
            // حساب المادة الجافة (كغ/يوم)
            const metabolicWeight = Math.pow(weight, 0.75);
            const fcm = milk * 1.05; // 4% fat corrected milk
            
            let baseDM = (0.372 * fcm + 0.0968 * metabolicWeight) / 100 * weight;
            baseDM = Math.max(baseDM, weight * 0.025); // الحد الأدنى 2.5% من الوزن
            
            const totalDM = baseDM * stageFactor.dm * activityFactor.dm;
            
            // حساب البروتين (غرام/يوم)
            const maintenanceProtein = metabolicWeight * 3.8;
            const milkProtein = milk * 78; // 78 غرام بروتين لكل لتر
            const totalProtein = (maintenanceProtein + milkProtein) * stageFactor.protein;
            
            // حساب الطاقة (Mcal/يوم) 
            const maintenanceEnergy = metabolicWeight * 0.08;
            const milkEnergy = milk * 0.75; // 0.75 Mcal لكل لتر
            const totalEnergy = (maintenanceEnergy + milkEnergy) * activityFactor.energy;
            
            // حساب الحد الأقصى للألياف
            const maxFiber = totalDM * 0.45;
            
            // توزيع العلف
            const concentrateFeed = Math.min(milk * 0.45, totalDM * 0.6);
            const roughageFeed = totalDM - concentrateFeed;
            const supplementFeed = totalDM * 0.05;
            
            // حساب التكلفة (افتراضية)
            const avgFeedPrice = 0.35; // د.أ/كغ متوسط
            const dailyCost = totalDM * avgFeedPrice;
            const monthlyCost = dailyCost * 30;
            const yearlyCost = dailyCost * 365;
            const costPerLiter = milk > 0 ? dailyCost / milk : 0;
            const costPerKg = dailyCost / totalDM;
            const feedEfficiency = milk > 0 ? totalDM / milk : 0;
            
            return {
                total_dm: Math.round(totalDM * 100) / 100,
                total_protein: Math.round(totalProtein),
                total_energy: Math.round(totalEnergy * 100) / 100,
                max_fiber: Math.round(maxFiber * 100) / 100,
                concentrate_feed: Math.round(concentrateFeed * 100) / 100,
                roughage_feed: Math.round(roughageFeed * 100) / 100,
                supplement_feed: Math.round(supplementFeed * 100) / 100,
                daily_cost: Math.round(dailyCost * 1000) / 1000,
                monthly_cost: Math.round(monthlyCost * 100) / 100,
                yearly_cost: Math.round(yearlyCost * 100) / 100,
                cost_per_liter: Math.round(costPerLiter * 1000) / 1000,
                cost_per_kg: Math.round(costPerKg * 1000) / 1000,
                feed_efficiency: Math.round(feedEfficiency * 100) / 100,
                weight: weight,
                milk_production: milk,
                lactation_stage: stage,
                activity_level: activity
            };
        }

        function testCalculation() {
            try {
                const weight = parseFloat(document.getElementById('cowWeight').value);
                const milk = parseFloat(document.getElementById('cowMilk').value);
                const stage = document.getElementById('cowStage').value;
                const activity = document.getElementById('activityLevel').value;
                
                console.log('المدخلات:', { weight, milk, stage, activity });
                
                if (!weight || weight < 350 || weight > 800) {
                    throw new Error('وزن البقرة يجب أن يكون بين 350-800 كغ');
                }
                
                if (milk < 0 || milk > 80) {
                    throw new Error('إنتاج الحليب يجب أن يكون بين 0-80 لتر');
                }
                
                const results = calculateNutritionalRequirements(weight, milk, stage, activity);
                console.log('النتائج:', results);
                
                // عرض النتائج
                const resultsContent = document.getElementById('resultsContent');
                resultsContent.innerHTML = `
                    <p><strong>المادة الجافة:</strong> ${results.total_dm} كغ/يوم</p>
                    <p><strong>البروتين:</strong> ${results.total_protein} غرام/يوم</p>
                    <p><strong>الطاقة:</strong> ${results.total_energy} Mcal/يوم</p>
                    <p><strong>الحد الأقصى للألياف:</strong> ${results.max_fiber} كغ/يوم</p>
                    <p><strong>العلف المركز:</strong> ${results.concentrate_feed} كغ</p>
                    <p><strong>العلف الخشن:</strong> ${results.roughage_feed} كغ</p>
                    <p><strong>الإضافات:</strong> ${results.supplement_feed} كغ</p>
                    <p><strong>التكلفة اليومية:</strong> ${results.daily_cost} د.أ</p>
                    <p><strong>التكلفة الشهرية:</strong> ${results.monthly_cost} د.أ</p>
                    <p><strong>التكلفة/لتر حليب:</strong> ${results.cost_per_liter} د.أ</p>
                    <p><strong>كفاءة التحويل:</strong> ${results.feed_efficiency} كغ علف/لتر</p>
                `;
                
                document.getElementById('results').style.display = 'block';
                
            } catch (error) {
                alert('خطأ في الحساب: ' + error.message);
                console.error('خطأ:', error);
            }
        }
    </script>
</body>
</html>