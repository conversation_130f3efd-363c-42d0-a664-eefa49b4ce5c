#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def create_test_cow_record():
    """إنشاء سجل اختبار لبقرة"""
    
    base_url = "http://localhost:5000"
    
    print("🐄 إنشاء سجل اختبار لبقرة...")
    
    # بيانات البقرة للاختبار
    cow_data = {
        "cow_name": "أم فهد",
        "weight": 550,
        "milk_production": 25,
        "lactation_stage": "mid",
        "activity_level": "moderate",
        "requirements": {
            "totalDM": 24.26,
            "totalProtein": 1882,
            "totalEnergy": 28.49,
            "dailyCost": 9.645
        }
    }
    
    try:
        response = requests.post(f"{base_url}/save_cow_calculation", json=cow_data)
        
        if response.status_code == 200:
            print("✅ تم إنشاء سجل البقرة بنجاح!")
            print(f"اسم البقرة: {cow_data['cow_name']}")
            print(f"الوزن: {cow_data['weight']} كغ")
            print(f"إنتاج الحليب: {cow_data['milk_production']} لتر/يوم")
            
            # الآن اختبر جلب التفاصيل
            print("\n🔍 اختبار جلب تفاصيل السجل...")
            
            # جرب مع ID = 1
            response = requests.get(f"{base_url}/cow_calculation/1")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ نجح جلب التفاصيل!")
                print(f"معرف السجل: {data['id']}")
                print(f"اسم البقرة: {data['cow_name']}")
                print(f"الوزن: {data['weight']} كغ")
                print(f"إنتاج الحليب: {data['milk_production']} لتر")
                print(f"المادة الجافة: {data['total_dm']} كغ/يوم")
                print(f"البروتين: {data['total_protein']} غرام/يوم")
                print(f"الطاقة: {data['total_energy']} Mcal/يوم")
                print(f"التكلفة: {data['daily_cost']} ريال/يوم")
                
                return data['id']  # إرجاع معرف السجل
            else:
                print(f"❌ فشل في جلب التفاصيل: {response.text}")
                return None
                
        else:
            print(f"❌ فشل في إنشاء السجل: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return None

def test_buttons_with_real_data(calc_id):
    """اختبار الأزرار مع بيانات حقيقية"""
    
    base_url = "http://localhost:5000"
    
    print(f"\n🧪 اختبار الأزرار مع السجل {calc_id}...")
    
    # اختبار عرض التفاصيل
    print("1. اختبار عرض التفاصيل...")
    try:
        response = requests.get(f"{base_url}/cow_calculation/{calc_id}")
        if response.status_code == 200:
            print("✅ زر عرض التفاصيل يعمل بشكل صحيح")
        else:
            print(f"❌ مشكلة في عرض التفاصيل: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في عرض التفاصيل: {e}")
    
    # اختبار إعادة الحساب
    print("\n2. اختبار إعادة الحساب...")
    try:
        response = requests.get(f"{base_url}/recalculate_cow/{calc_id}", allow_redirects=False)
        if response.status_code == 302:  # إعادة توجيه
            print("✅ زر إعادة الحساب يعمل بشكل صحيح")
            print(f"إعادة توجيه إلى: {response.headers.get('Location', 'غير محدد')}")
        else:
            print(f"❌ مشكلة في إعادة الحساب: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في إعادة الحساب: {e}")
    
    # اختبار الحذف (اختياري)
    print("\n3. اختبار الحذف...")
    try:
        response = requests.post(f"{base_url}/delete_cow_calculation/{calc_id}")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ زر الحذف يعمل بشكل صحيح")
                print(f"رسالة: {data.get('message', '')}")
            else:
                print(f"❌ فشل الحذف: {data.get('message', '')}")
        else:
            print(f"❌ مشكلة في الحذف: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في الحذف: {e}")

if __name__ == "__main__":
    # إنشاء سجل اختبار
    calc_id = create_test_cow_record()
    
    if calc_id:
        # اختبار الأزرار
        test_buttons_with_real_data(calc_id)
        
        print("\n🎉 تم الانتهاء من الاختبار!")
        print("الآن يمكنك فتح صفحة تاريخ الأبقار واختبار الأزرار بنفسك:")
        print("http://localhost:5000/cow_history")
    else:
        print("\n❌ لم يتم إنشاء سجل الاختبار")
