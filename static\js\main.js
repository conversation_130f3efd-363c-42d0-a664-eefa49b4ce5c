// وظائف JavaScript الرئيسية لبرنامج تكوين الأعلاف المركزة

document.addEventListener('DOMContentLoaded', function() {
    console.log('برنامج تكوين الأعلاف المركزة - تم تحميل الصفحة');
    
    // تفعيل التلميحات (Tooltips)
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // إضافة تأثيرات الحركة للبطاقات
    addCardAnimations();
    
    // تفعيل التحقق من صحة النماذج
    validateForms();
    
    // إضافة مستمعات الأحداث للمدخلات الرقمية
    setupNumericInputs();
});

// إضافة تأثيرات الحركة للبطاقات
function addCardAnimations() {
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in');
    });
}

// تفعيل التحقق من صحة النماذج
function validateForms() {
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
}

// إعداد المدخلات الرقمية
function setupNumericInputs() {
    const numericInputs = document.querySelectorAll('input[type="number"]');
    numericInputs.forEach(input => {
        input.addEventListener('input', function() {
            // التأكد من أن القيمة ليست سالبة
            if (this.value < 0) {
                this.value = 0;
            }
            
            // تحديث المعاينة الفورية إذا كانت متاحة
            updatePreview();
        });
        
        // إضافة تأثير التركيز
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
}

// تحديث المعاينة الفورية للحسابات
function updatePreview() {
    const quantityInputs = document.querySelectorAll('.ingredient-quantity');
    let totalWeight = 0;
    let hasValues = false;
    
    quantityInputs.forEach(input => {
        const value = parseFloat(input.value) || 0;
        if (value > 0) {
            hasValues = true;
            totalWeight += value;
        }
    });
    
    // عرض الوزن الإجمالي إذا كان متاحاً
    const previewElement = document.getElementById('weightPreview');
    if (previewElement) {
        if (hasValues) {
            previewElement.textContent = `الوزن الإجمالي: ${totalWeight.toFixed(2)} كغ`;
            previewElement.style.display = 'block';
        } else {
            previewElement.style.display = 'none';
        }
    }
}

// إضافة تأثيرات تفاعلية للأزرار
function addButtonEffects() {
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // إضافة تأثير الضغط
            const ripple = document.createElement('span');
            ripple.classList.add('ripple');
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 300);
        });
    });
}

// وظيفة لإظهار رسائل التحميل
function showLoading(element, message = 'جاري التحميل...') {
    const originalContent = element.innerHTML;
    element.innerHTML = `
        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
        ${message}
    `;
    element.disabled = true;
    
    return originalContent;
}

// وظيفة لإخفاء رسائل التحميل
function hideLoading(element, originalContent) {
    element.innerHTML = originalContent;
    element.disabled = false;
}

// وظيفة لعرض الرسائل التفاعلية
function showMessage(message, type = 'success', duration = 3000) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // إضافة الرسالة في أعلى الصفحة
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // إزالة الرسالة تلقائياً
    setTimeout(() => {
        alertDiv.remove();
    }, duration);
}

// وظيفة لتنسيق الأرقام
function formatNumber(number, decimals = 2) {
    return parseFloat(number).toFixed(decimals);
}

// وظيفة لتنسيق الأرقام العربية
function formatArabicNumber(number) {
    const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().replace(/\d/g, (digit) => arabicNumerals[parseInt(digit)]);
}

// وظيفة للتحقق من صحة البيانات المدخلة
function validateMixInputs() {
    const inputs = document.querySelectorAll('.ingredient-quantity');
    let isValid = false;
    let totalWeight = 0;
    
    inputs.forEach(input => {
        const value = parseFloat(input.value) || 0;
        if (value > 0) {
            isValid = true;
            totalWeight += value;
        }
    });
    
    if (!isValid) {
        showMessage('يرجى إدخال كميات المكونات أولاً', 'warning');
        return false;
    }
    
    if (totalWeight > 1000) {
        const confirm = window.confirm('الوزن الإجمالي للخلطة كبير جداً. هل تريد المتابعة؟');
        if (!confirm) return false;
    }
    
    return true;
}

// وظيفة لحفظ البيانات محلياً
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
        return true;
    } catch (error) {
        console.error('خطأ في حفظ البيانات محلياً:', error);
        return false;
    }
}

// وظيفة لاسترجاع البيانات محلياً
function loadFromLocalStorage(key) {
    try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
    } catch (error) {
        console.error('خطأ في استرجاع البيانات محلياً:', error);
        return null;
    }
}

// وظيفة لإضافة تأثيرات بصرية للجداول
function enhanceTable(tableElement) {
    const rows = tableElement.querySelectorAll('tbody tr');
    rows.forEach((row, index) => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
            this.style.transform = 'scale(1.01)';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
            this.style.transform = 'scale(1)';
        });
    });
}

// وظيفة لإنشاء رسم بياني بسيط للقيم الغذائية
function createNutritionChart(protein, energy, fiber) {
    const chartContainer = document.createElement('div');
    chartContainer.className = 'nutrition-chart mt-3';
    
    const proteinBar = createNutritionBar('البروتين', protein, 'protein');
    const energyBar = createNutritionBar('الطاقة', energy, 'energy');
    const fiberBar = createNutritionBar('الألياف', fiber, 'fiber');
    
    chartContainer.appendChild(proteinBar);
    chartContainer.appendChild(energyBar);
    chartContainer.appendChild(fiberBar);
    
    return chartContainer;
}

// إنشاء شريط تقدم للقيم الغذائية
function createNutritionBar(label, value, type) {
    const barContainer = document.createElement('div');
    barContainer.className = 'nutrition-bar-container mb-2';
    
    const labelElement = document.createElement('label');
    labelElement.textContent = `${label}: ${value}`;
    labelElement.className = 'form-label';
    
    const barElement = document.createElement('div');
    barElement.className = 'nutrition-bar';
    
    const fillElement = document.createElement('div');
    fillElement.className = `nutrition-fill ${type}-fill`;
    
    // تحديد عرض الشريط بناءً على القيمة
    let width = 0;
    if (type === 'protein') {
        width = Math.min((value / 30) * 100, 100); // البروتين حتى 30%
    } else if (type === 'energy') {
        width = Math.min((value / 4000) * 100, 100); // الطاقة حتى 4000 kcal/kg
    } else if (type === 'fiber') {
        width = Math.min((value / 15) * 100, 100); // الألياف حتى 15%
    }
    
    fillElement.style.width = `${width}%`;
    fillElement.textContent = value;
    
    barElement.appendChild(fillElement);
    barContainer.appendChild(labelElement);
    barContainer.appendChild(barElement);
    
    return barContainer;
}

// تفعيل جميع التحسينات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    addButtonEffects();
    
    // تحسين الجداول
    const tables = document.querySelectorAll('.table');
    tables.forEach(enhanceTable);
    
    // إضافة معاينة فورية للوزن
    const previewContainer = document.querySelector('.card-body');
    if (previewContainer && !document.getElementById('weightPreview')) {
        const previewElement = document.createElement('div');
        previewElement.id = 'weightPreview';
        previewElement.className = 'alert alert-info mt-2';
        previewElement.style.display = 'none';
        previewContainer.appendChild(previewElement);
    }
});

// إضافة CSS للتأثيرات
const style = document.createElement('style');
style.textContent = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.6);
        animation: ripple-animation 0.3s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        0% {
            transform: scale(0);
            opacity: 1;
        }
        100% {
            transform: scale(2);
            opacity: 0;
        }
    }
    
    .focused {
        transform: scale(1.02);
        transition: transform 0.2s ease;
    }
`;
document.head.appendChild(style);