#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_component_display():
    """اختبار عرض مكونات الخلطة المقترحة في الصفحة الرئيسية"""
    
    base_url = "http://localhost:5000"
    
    print("🧪 اختبار عرض مكونات الخلطة المقترحة")
    print("=" * 50)
    
    # الخطوة 1: حساب احتياجات البقرة
    print("1️⃣ حساب احتياجات البقرة...")
    
    cow_data = {
        "animal_type": "cow",
        "cow_weight": 400,
        "cow_milk": 20,
        "cow_stage": "mid",
        "activity_level": "moderate"
    }
    
    try:
        response = requests.post(f"{base_url}/calculate_animal_requirements", json=cow_data)
        
        if response.status_code == 200:
            requirements_data = response.json()
            print("   ✅ تم حساب الاحتياجات")
            
            requirements = requirements_data['requirements']
            print(f"   📊 المادة الجافة: {requirements['total_dm']} كغ/يوم")
            print(f"   🥩 البروتين: {requirements['total_protein']} غرام/يوم")
            
        else:
            print(f"   ❌ فشل: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ: {e}")
        return False
    
    # الخطوة 2: اقتراح الخلطة
    print("\n2️⃣ اقتراح الخلطة...")
    
    try:
        response = requests.post(f"{base_url}/suggest_mix_for_cow", 
                               json={"requirements": requirements})
        
        if response.status_code == 200:
            mix_data = response.json()
            
            if mix_data.get('success'):
                print("   ✅ تم اقتراح الخلطة")
                
                suggested_mix = mix_data['suggested_mix']
                
                print(f"   📋 عدد المكونات: {len(suggested_mix['mix_components'])}")
                print(f"   💰 إجمالي التكلفة: {suggested_mix['totals']['cost']} د.أ")
                
                # عرض المكونات بالتفصيل
                print("\n   🌾 مكونات الخلطة المقترحة:")
                for i, component in enumerate(suggested_mix['mix_components'], 1):
                    ingredient = component['ingredient']
                    print(f"      {i}. {ingredient['name']}")
                    print(f"         - الكمية: {component['quantity']} كغ")
                    print(f"         - النسبة: {component['percentage']}%")
                    print(f"         - البروتين: {ingredient['protein']}%")
                    print(f"         - الطاقة: {ingredient['energy']} kcal/kg")
                    print(f"         - السعر: {ingredient['price_per_kg']} د.أ/كغ")
                    print()
                
                # محاكاة حفظ البيانات في Local Storage
                print("3️⃣ محاكاة حفظ البيانات في Local Storage...")
                
                mix_data_for_storage = {
                    'name': 'خلطة مقترحة للبقرة - اختبار',
                    'components': suggested_mix['mix_components']
                }
                
                print("   📝 البيانات التي ستُحفظ في Local Storage:")
                print(f"   📋 اسم الخلطة: {mix_data_for_storage['name']}")
                print(f"   📊 عدد المكونات: {len(mix_data_for_storage['components'])}")
                
                for component in mix_data_for_storage['components']:
                    ingredient = component['ingredient']
                    print(f"      • {ingredient['name']}: {component['quantity']} كغ")
                
                print("\n4️⃣ التحقق من تنسيق البيانات...")
                
                # التحقق من أن جميع الحقول المطلوبة موجودة
                required_fields = ['id', 'name', 'protein', 'energy', 'price_per_kg']
                all_fields_ok = True
                
                for component in mix_data_for_storage['components']:
                    ingredient = component['ingredient']
                    for field in required_fields:
                        if field not in ingredient:
                            print(f"   ❌ حقل مفقود: {field} في {ingredient.get('name', 'مكون غير معروف')}")
                            all_fields_ok = False
                
                if all_fields_ok:
                    print("   ✅ جميع الحقول المطلوبة موجودة")
                else:
                    print("   ❌ بعض الحقول مفقودة")
                    return False
                
                print("\n5️⃣ اختبار الصفحة الرئيسية...")
                
                # التحقق من أن الصفحة الرئيسية تحتوي على الدوال المطلوبة
                response = requests.get(f"{base_url}/")
                
                if response.status_code == 200:
                    content = response.text
                    
                    checks = [
                        ("checkForSuggestedMix", "فحص الخلطة المقترحة"),
                        ("loadSuggestedMix", "تحميل الخلطة المقترحة"),
                        ("updateMixtureTable", "تحديث جدول الخلطة"),
                        ("updateQuickPreview", "تحديث المعاينة السريعة"),
                        ("mixtureIngredients", "متغير مكونات الخلطة")
                    ]
                    
                    all_functions_ok = True
                    for check, desc in checks:
                        if check in content:
                            print(f"   ✅ {desc}: موجود")
                        else:
                            print(f"   ❌ {desc}: مفقود")
                            all_functions_ok = False
                    
                    if all_functions_ok:
                        print("\n🎉 جميع الوظائف موجودة في الصفحة الرئيسية!")
                        print("\n💡 الآن عند الضغط على 'قبول الخلطة وحفظها':")
                        print("   1. ستُحفظ البيانات في Local Storage ✅")
                        print("   2. ستنتقل للصفحة الرئيسية ✅")
                        print("   3. ستُقرأ البيانات من Local Storage ✅")
                        print("   4. ستُحمل المكونات في متغير mixtureIngredients ✅")
                        print("   5. ستُحدث جدول الخلطة (updateMixtureTable) ✅")
                        print("   6. ستُحدث المعاينة السريعة (updateQuickPreview) ✅")
                        print("   7. ستظهر رسالة تأكيد مع قائمة المكونات ✅")
                        print("   8. ستُفعل أزرار الحساب والحفظ ✅")
                        
                        return True
                    else:
                        print("\n❌ بعض الوظائف مفقودة")
                        return False
                else:
                    print(f"   ❌ فشل في الوصول للصفحة الرئيسية: {response.status_code}")
                    return False
                
            else:
                print(f"   ❌ فشل في اقتراح الخلطة: {mix_data.get('error')}")
                return False
        else:
            print(f"   ❌ فشل: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ: {e}")
        return False

if __name__ == "__main__":
    print("🎯 اختبار عرض مكونات الخلطة المقترحة في الصفحة الرئيسية")
    print()
    
    success = test_component_display()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 الاختبار نجح! مكونات الخلطة ستظهر في الصفحة الرئيسية")
        print()
        print("🚀 للاختبار العملي:")
        print("   1. افتح: http://localhost:5000/cow_requirements")
        print("   2. أدخل بيانات البقرة")
        print("   3. اضغط 'حساب الاحتياجات'")
        print("   4. اضغط 'اقتراح خلطة مناسبة'")
        print("   5. اضغط 'قبول الخلطة وحفظها'")
        print("   6. ستنتقل للصفحة الرئيسية وتظهر المكونات! ✅")
    else:
        print("❌ الاختبار فشل - يحتاج مراجعة")
    
    print(f"\n🌐 الروابط:")
    print(f"   📊 احتياجات البقرة: http://localhost:5000/cow_requirements")
    print(f"   🏠 الصفحة الرئيسية: http://localhost:5000/")
