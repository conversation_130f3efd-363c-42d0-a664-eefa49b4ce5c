#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def add_sample_cow_data():
    """إضافة بيانات عينة من الأبقار للاختبار"""
    
    base_url = "http://localhost:5000"
    
    print("🐄 إضافة بيانات عينة من الأبقار...")
    
    # بيانات أبقار متنوعة للاختبار
    cows_data = [
        {
            "cow_name": "أم خالد",
            "weight": 500,
            "milk_production": 20,
            "lactation_stage": "early",
            "activity_level": "moderate",
            "requirements": {
                "totalDM": 22.5,
                "totalProtein": 1650,
                "totalEnergy": 26.2,
                "dailyCost": 8.9
            }
        },
        {
            "cow_name": "أم سعد",
            "weight": 600,
            "milk_production": 30,
            "lactation_stage": "mid",
            "activity_level": "high",
            "requirements": {
                "totalDM": 28.1,
                "totalProtein": 2150,
                "totalEnergy": 32.8,
                "dailyCost": 11.2
            }
        },
        {
            "cow_name": "أم محمد",
            "weight": 450,
            "milk_production": 15,
            "lactation_stage": "late",
            "activity_level": "low",
            "requirements": {
                "totalDM": 19.8,
                "totalProtein": 1420,
                "totalEnergy": 23.1,
                "dailyCost": 7.8
            }
        },
        {
            "cow_name": "أم عبدالله",
            "weight": 520,
            "milk_production": 22,
            "lactation_stage": "mid",
            "activity_level": "moderate",
            "requirements": {
                "totalDM": 23.7,
                "totalProtein": 1750,
                "totalEnergy": 27.5,
                "dailyCost": 9.3
            }
        },
        {
            "cow_name": "أم أحمد",
            "weight": 580,
            "milk_production": 28,
            "lactation_stage": "early",
            "activity_level": "high",
            "requirements": {
                "totalDM": 27.2,
                "totalProtein": 2050,
                "totalEnergy": 31.4,
                "dailyCost": 10.8
            }
        }
    ]
    
    success_count = 0
    
    for i, cow_data in enumerate(cows_data, 1):
        try:
            response = requests.post(f"{base_url}/save_cow_calculation", json=cow_data)
            
            if response.status_code == 200:
                print(f"✅ {i}. تم حفظ بيانات {cow_data['cow_name']} بنجاح")
                success_count += 1
            else:
                print(f"❌ {i}. فشل في حفظ {cow_data['cow_name']}: {response.text}")
                
        except Exception as e:
            print(f"❌ {i}. خطأ في حفظ {cow_data['cow_name']}: {e}")
    
    print(f"\n🎯 تم حفظ {success_count} من {len(cows_data)} أبقار بنجاح!")
    
    if success_count > 0:
        print("\n📊 يمكنك الآن فتح صفحة تاريخ الأبقار لرؤية البيانات:")
        print("http://localhost:5000/cow_history")
        
        print("\n🔧 اختبر الأزرار التالية:")
        print("👁️ عرض التفاصيل - لرؤية تفاصيل كاملة عن البقرة")
        print("🔄 إعادة حساب - لإعادة حساب احتياجات البقرة بنفس البيانات")
        print("🗑️ حذف - لحذف سجل البقرة من قاعدة البيانات")

if __name__ == "__main__":
    add_sample_cow_data()
