('C:\\Users\\<USER>\\Desktop\\New folder '
 '(5)\\build\\feed_mixer\\برنامج_تكوين_الاعلاف.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Desktop\\New folder (5)\\build\\feed_mixer\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\build\\feed_mixer\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\build\\feed_mixer\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\build\\feed_mixer\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\build\\feed_mixer\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\build\\feed_mixer\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('main', 'C:\\Users\\<USER>\\Desktop\\New folder (5)\\main.py', 'PYSOURCE'),
  ('python313.dll', 'C:\\Program Files\\Python313\\python313.dll', 'BINARY'),
  ('select.pyd', 'C:\\Program Files\\Python313\\DLLs\\select.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Program Files\\Python313\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'C:\\Program Files\\Python313\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Program Files\\Python313\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'C:\\Program Files\\Python313\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Program Files\\Python313\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'C:\\Program Files\\Python313\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_wmi.pyd', 'C:\\Program Files\\Python313\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp313-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\markupsafe\\_speedups.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'C:\\Program Files\\Python313\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Program Files\\Python313\\VCRUNTIME140.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Program Files\\Python313\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Program Files\\Python313\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Program Files\\Python313\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Program Files\\Python313\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('sqlite3.dll', 'C:\\Program Files\\Python313\\DLLs\\sqlite3.dll', 'BINARY'),
  ('flask\\py.typed',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask\\py.typed',
   'DATA'),
  ('flask\\sansio\\README.md',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask\\sansio\\README.md',
   'DATA'),
  ('jinja2\\py.typed',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\jinja2\\py.typed',
   'DATA'),
  ('static\\css\\style.css',
   'C:\\Users\\<USER>\\Desktop\\New folder (5)\\static\\css\\style.css',
   'DATA'),
  ('static\\js\\main.js',
   'C:\\Users\\<USER>\\Desktop\\New folder (5)\\static\\js\\main.js',
   'DATA'),
  ('templates\\animal_requirements.html',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\templates\\animal_requirements.html',
   'DATA'),
  ('templates\\base.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (5)\\templates\\base.html',
   'DATA'),
  ('templates\\compare_cows.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (5)\\templates\\compare_cows.html',
   'DATA'),
  ('templates\\cow_history.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (5)\\templates\\cow_history.html',
   'DATA'),
  ('templates\\cow_requirements.html',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\templates\\cow_requirements.html',
   'DATA'),
  ('templates\\index.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (5)\\templates\\index.html',
   'DATA'),
  ('templates\\manage_ingredients.html',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\templates\\manage_ingredients.html',
   'DATA'),
  ('templates\\saved_mixes.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (5)\\templates\\saved_mixes.html',
   'DATA'),
  ('templates\\view_mix.html',
   'C:\\Users\\<USER>\\Desktop\\New folder (5)\\templates\\view_mix.html',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.1.1.dist-info\\licenses\\LICENSE.txt',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('flask-3.1.1.dist-info\\REQUESTED',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\REQUESTED',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\INSTALLER',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.1.1.dist-info\\METADATA',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\METADATA',
   'DATA'),
  ('flask-3.1.1.dist-info\\INSTALLER',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\INSTALLER',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\RECORD',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.1.1.dist-info\\entry_points.txt',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\entry_points.txt',
   'DATA'),
  ('flask-3.1.1.dist-info\\RECORD',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\METADATA',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\METADATA',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\WHEEL',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\WHEEL',
   'DATA'),
  ('flask-3.1.1.dist-info\\WHEEL',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\WHEEL',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\build\\feed_mixer\\base_library.zip',
   'DATA')],
 'python313.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
