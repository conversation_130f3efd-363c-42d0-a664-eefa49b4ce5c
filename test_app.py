#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل لبرنامج تكوين الأعلاف المركزة
"""

from app_simple import app, init_db, get_db

def test_database():
    """اختبار قاعدة البيانات"""
    print("🔍 اختبار قاعدة البيانات...")
    
    try:
        init_db()
        conn = get_db()
        
        # اختبار الجداول
        tables = conn.execute("SELECT name FROM sqlite_master WHERE type='table'").fetchall()
        print("✅ قاعدة البيانات تعمل بشكل صحيح")
        print("📊 الجداول المتاحة:")
        for table in tables:
            print(f"   - {table[0]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_routes():
    """اختبار جميع المسارات"""
    print("\n🔍 اختبار جميع المسارات...")
    
    routes_to_test = [
        '/',
        '/manage_ingredients', 
        '/cow_requirements',
        '/animal_requirements',
        '/cow_history',
        '/compare_cows',
        '/saved_mixes'
    ]
    
    with app.test_client() as client:
        for route in routes_to_test:
            try:
                response = client.get(route)
                if response.status_code == 200:
                    print(f"✅ {route} - يعمل بشكل صحيح")
                else:
                    print(f"⚠️ {route} - رمز الاستجابة: {response.status_code}")
            except Exception as e:
                print(f"❌ {route} - خطأ: {e}")

def test_functions():
    """اختبار الوظائف الأساسية"""
    print("\n🔍 اختبار الوظائف الأساسية...")
    
    try:
        from app_simple import calculate_nutritional_requirements
        
        # اختبار حساب احتياجات البقرة
        result = calculate_nutritional_requirements(550, 25, 'mid', 'moderate')
        if result and 'total_dm' in result:
            print("✅ حساب احتياجات البقرة يعمل بشكل صحيح")
        else:
            print("❌ مشكلة في حساب احتياجات البقرة")
            
    except Exception as e:
        print(f"❌ خطأ في الوظائف: {e}")

def main():
    """الوظيفة الرئيسية للاختبار"""
    print("🧪 بدء الاختبار الشامل لبرنامج تكوين الأعلاف المركزة")
    print("=" * 60)
    
    # اختبار قاعدة البيانات
    db_ok = test_database()
    
    # اختبار المسارات
    test_routes()
    
    # اختبار الوظائف
    test_functions()
    
    print("\n" + "=" * 60)
    if db_ok:
        print("🎉 الاختبار مكتمل! البرنامج جاهز للاستخدام")
        print("🚀 يمكنك تشغيل البرنامج باستخدام: python run.py")
    else:
        print("⚠️ هناك بعض المشاكل تحتاج للإصلاح")

if __name__ == "__main__":
    main()