<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مبسط</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .btn { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .form-control { padding: 8px; margin: 5px; width: 200px; }
        .results { margin-top: 20px; padding: 15px; background: #f8f9fa; border: 1px solid #dee2e6; }
    </style>
</head>
<body>
    <h1>🧪 اختبار مبسط لزر حساب الاحتياجات</h1>
    
    <div>
        <label>وزن البقرة (كغ):</label><br>
        <input type="number" id="cowWeight" class="form-control" value="500" min="300" max="800">
    </div>
    
    <div>
        <label>إنتاج الحليب (لتر/يوم):</label><br>
        <input type="number" id="milkProduction" class="form-control" value="25" min="0" max="80">
    </div>
    
    <div>
        <label>مرحلة الإنتاج:</label><br>
        <select id="lactationStage" class="form-control">
            <option value="early">مبكرة</option>
            <option value="mid" selected>متوسطة</option>
            <option value="late">متأخرة</option>
        </select>
    </div>
    
    <div>
        <label>مستوى النشاط:</label><br>
        <select id="activityLevel" class="form-control">
            <option value="low">منخفض</option>
            <option value="moderate" selected>متوسط</option>
            <option value="high">مرتفع</option>
        </select>
    </div>
    
    <div style="margin-top: 20px;">
        <button id="calculateBtn" class="btn">🧮 حساب الاحتياجات</button>
        <button id="testBtn" class="btn" style="background: #28a745;">🧪 اختبار بسيط</button>
    </div>
    
    <div id="results" class="results" style="display: none;">
        <h3>📊 النتائج:</h3>
        <div id="resultContent"></div>
    </div>
    
    <div class="results">
        <h3>📝 سجل الأحداث:</h3>
        <div id="log" style="max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;"></div>
    </div>

    <script>
        console.log('🚀 بدء تحميل JavaScript...');
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function testSimple() {
            log('🧪 اختبار بسيط - تم الضغط على الزر!');
            alert('الاختبار البسيط يعمل! ✅');
        }
        
        function calculateRequirements() {
            log('🐄 تم الضغط على زر حساب الاحتياجات!');
            alert('تم استدعاء دالة حساب الاحتياجات! ✅');
            
            const cowWeight = parseFloat(document.getElementById('cowWeight').value);
            const milkProduction = parseFloat(document.getElementById('milkProduction').value);
            const lactationStage = document.getElementById('lactationStage').value;
            const activityLevel = document.getElementById('activityLevel').value;
            
            log(`📊 البيانات: وزن=${cowWeight}, حليب=${milkProduction}, مرحلة=${lactationStage}, نشاط=${activityLevel}`);
            
            if (!cowWeight || cowWeight < 300 || cowWeight > 800) {
                log('❌ وزن البقرة غير صحيح');
                alert('يرجى إدخال وزن صحيح للبقرة (300-800 كغ)');
                return;
            }
            
            if (milkProduction < 0 || milkProduction > 80) {
                log('❌ إنتاج الحليب غير صحيح');
                alert('يرجى إدخال إنتاج حليب صحيح (0-80 لتر)');
                return;
            }
            
            const calculateBtn = document.getElementById('calculateBtn');
            const originalText = calculateBtn.innerHTML;
            calculateBtn.innerHTML = '⏳ جاري الحساب...';
            calculateBtn.disabled = true;
            
            log('🌐 إرسال البيانات إلى الخادم...');
            
            const requestData = {
                animal_type: 'cow',
                cow_weight: cowWeight,
                cow_milk: milkProduction,
                cow_stage: lactationStage,
                activity_level: activityLevel
            };
            
            fetch('http://localhost:5000/calculate_animal_requirements', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                log(`📡 كود الاستجابة: ${response.status}`);
                return response.json();
            })
            .then(data => {
                log(`📥 تم استقبال البيانات بنجاح!`);
                
                if (data.error) {
                    log(`❌ خطأ: ${data.error}`);
                    alert('خطأ: ' + data.error);
                    return;
                }
                
                if (data.requirements) {
                    const req = data.requirements;
                    const resultContent = document.getElementById('resultContent');
                    
                    resultContent.innerHTML = `
                        <p><strong>المادة الجافة:</strong> ${req.total_dm} كغ/يوم</p>
                        <p><strong>البروتين:</strong> ${(req.total_protein / 1000).toFixed(2)} كغ/يوم</p>
                        <p><strong>الطاقة:</strong> ${req.total_energy} Mcal/يوم</p>
                        <p><strong>التكلفة اليومية:</strong> ${req.daily_cost} ريال/يوم</p>
                        <p><strong>نسبة البروتين:</strong> ${req.protein_percentage}%</p>
                    `;
                    
                    document.getElementById('results').style.display = 'block';
                    log('✅ تم عرض النتائج بنجاح!');
                } else {
                    log('❌ البيانات لا تحتوي على requirements');
                    alert('خطأ في البيانات المرجعة');
                }
            })
            .catch(error => {
                log(`❌ خطأ في الاتصال: ${error.message}`);
                alert('خطأ في الاتصال: ' + error.message);
            })
            .finally(() => {
                calculateBtn.innerHTML = originalText;
                calculateBtn.disabled = false;
                log('🔄 تم إعادة تعيين الزر');
            });
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            log('📄 تم تحميل الصفحة');
            
            const calculateBtn = document.getElementById('calculateBtn');
            const testBtn = document.getElementById('testBtn');
            
            if (calculateBtn) {
                calculateBtn.addEventListener('click', calculateRequirements);
                log('✅ تم ربط زر حساب الاحتياجات');
            } else {
                log('❌ لم يتم العثور على زر حساب الاحتياجات');
            }
            
            if (testBtn) {
                testBtn.addEventListener('click', testSimple);
                log('✅ تم ربط زر الاختبار البسيط');
            } else {
                log('❌ لم يتم العثور على زر الاختبار');
            }
            
            log('🎯 جاهز للاختبار!');
        });
        
        log('🔧 تم تحميل JavaScript');
    </script>
</body>
</html>
