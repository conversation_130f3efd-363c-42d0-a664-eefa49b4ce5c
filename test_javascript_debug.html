<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار JavaScript - حساب احتياجات البقرة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>🧪 اختبار JavaScript</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="cowWeight" class="form-label">وزن البقرة (كغ)</label>
                            <input type="number" class="form-control" id="cowWeight" value="500">
                        </div>
                        
                        <div class="mb-3">
                            <label for="milkProduction" class="form-label">إنتاج الحليب (لتر/يوم)</label>
                            <input type="number" class="form-control" id="milkProduction" value="25">
                        </div>
                        
                        <div class="mb-3">
                            <label for="lactationStage" class="form-label">مرحلة الإنتاج</label>
                            <select class="form-select" id="lactationStage">
                                <option value="early">مبكرة</option>
                                <option value="mid" selected>متوسطة</option>
                                <option value="late">متأخرة</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="activityLevel" class="form-label">مستوى النشاط</label>
                            <select class="form-select" id="activityLevel">
                                <option value="low">منخفض</option>
                                <option value="moderate" selected>متوسط</option>
                                <option value="high">مرتفع</option>
                            </select>
                        </div>
                        
                        <button type="button" class="btn btn-success" id="calculateBtn">
                            <i class="fas fa-calculator me-2"></i>
                            حساب الاحتياجات
                        </button>
                        
                        <button type="button" class="btn btn-info ms-2" id="testApiBtn">
                            <i class="fas fa-flask me-2"></i>
                            اختبار API مباشر
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>📊 النتائج</h5>
                    </div>
                    <div class="card-body">
                        <div id="results">
                            <p class="text-muted">اضغط على "حساب الاحتياجات" لرؤية النتائج</p>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>🔍 سجل التشخيص</h5>
                    </div>
                    <div class="card-body">
                        <div id="debugLog" style="max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;">
                            <p class="text-muted">سيظهر هنا سجل التشخيص...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // متغيرات عامة
        let currentRequirements = null;
        
        // دالة إضافة رسالة للسجل
        function addToLog(message, type = 'info') {
            const log = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'text-danger' : type === 'success' ? 'text-success' : 'text-info';
            log.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
            log.scrollTop = log.scrollHeight;
        }
        
        // دالة حساب الاحتياجات
        function calculateRequirements() {
            addToLog('🐄 بدء حساب احتياجات البقرة...');
            
            const cowWeight = parseFloat(document.getElementById('cowWeight').value);
            const milkProduction = parseFloat(document.getElementById('milkProduction').value);
            const lactationStage = document.getElementById('lactationStage').value;
            const activityLevel = document.getElementById('activityLevel').value;
            
            addToLog(`البيانات المدخلة: وزن=${cowWeight}, حليب=${milkProduction}, مرحلة=${lactationStage}, نشاط=${activityLevel}`);
            
            if (!cowWeight || cowWeight < 300 || cowWeight > 800) {
                addToLog('❌ وزن البقرة غير صحيح', 'error');
                alert('يرجى إدخال وزن صحيح للبقرة (300-800 كغ)');
                return;
            }
            
            if (milkProduction < 0 || milkProduction > 80) {
                addToLog('❌ إنتاج الحليب غير صحيح', 'error');
                alert('يرجى إدخال إنتاج حليب صحيح (0-80 لتر)');
                return;
            }
            
            // إظهار مؤشر التحميل
            const calculateBtn = document.getElementById('calculateBtn');
            const originalText = calculateBtn.innerHTML;
            calculateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحساب...';
            calculateBtn.disabled = true;
            
            addToLog('🌐 إرسال البيانات إلى الخادم...');
            
            const requestData = {
                animal_type: 'cow',
                cow_weight: cowWeight,
                cow_milk: milkProduction,
                cow_stage: lactationStage,
                activity_level: activityLevel
            };
            
            addToLog(`📤 البيانات المرسلة: ${JSON.stringify(requestData)}`);
            
            fetch('/calculate_animal_requirements', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                addToLog(`📡 كود الاستجابة: ${response.status}`);
                return response.json();
            })
            .then(data => {
                addToLog(`📥 البيانات المستقبلة: ${JSON.stringify(data, null, 2)}`);
                
                if (data.error) {
                    addToLog(`❌ خطأ من الخادم: ${data.error}`, 'error');
                    alert('خطأ: ' + data.error);
                    return;
                }
                
                if (data.requirements) {
                    addToLog('✅ تم استقبال البيانات بنجاح!', 'success');
                    currentRequirements = data.requirements;
                    displayRequirements(data.requirements);
                } else {
                    addToLog('❌ البيانات المرجعة لا تحتوي على requirements', 'error');
                    alert('خطأ في استقبال البيانات من الخادم');
                }
            })
            .catch(error => {
                addToLog(`❌ خطأ في الاتصال: ${error.message}`, 'error');
                console.error('خطأ في الاتصال:', error);
                alert('خطأ في الاتصال بالخادم: ' + error.message);
            })
            .finally(() => {
                // إعادة تعيين زر الحساب
                calculateBtn.innerHTML = originalText;
                calculateBtn.disabled = false;
                addToLog('🔄 تم إعادة تعيين الزر');
            });
        }
        
        // دالة عرض النتائج
        function displayRequirements(req) {
            addToLog('📊 عرض النتائج...');
            
            const resultsDiv = document.getElementById('results');
            
            const html = `
                <div class="row">
                    <div class="col-6">
                        <div class="card bg-primary text-white mb-2">
                            <div class="card-body text-center">
                                <h6>المادة الجافة</h6>
                                <h4>${req.total_dm ? req.total_dm.toFixed(1) : '0.0'} كغ</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="card bg-success text-white mb-2">
                            <div class="card-body text-center">
                                <h6>البروتين</h6>
                                <h4>${req.total_protein ? (req.total_protein / 1000).toFixed(2) : '0.0'} كغ</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="card bg-warning text-white mb-2">
                            <div class="card-body text-center">
                                <h6>الطاقة</h6>
                                <h4>${req.total_energy ? req.total_energy.toFixed(1) : '0.0'} Mcal</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="card bg-info text-white mb-2">
                            <div class="card-body text-center">
                                <h6>التكلفة اليومية</h6>
                                <h4>${req.daily_cost ? req.daily_cost.toFixed(2) : '0.0'} ريال</h4>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <h6>تفاصيل إضافية:</h6>
                    <ul>
                        <li>نسبة البروتين: ${req.protein_percentage ? req.protein_percentage.toFixed(1) : '0.0'}%</li>
                        <li>الألياف الدنيا: ${req.min_fiber ? req.min_fiber.toFixed(1) : '0.0'} كغ</li>
                        <li>الألياف العليا: ${req.max_fiber ? req.max_fiber.toFixed(1) : '0.0'} كغ</li>
                    </ul>
                </div>
            `;
            
            resultsDiv.innerHTML = html;
            addToLog('✅ تم عرض النتائج بنجاح!', 'success');
        }
        
        // دالة اختبار API مباشر
        function testApiDirect() {
            addToLog('🧪 اختبار API مباشر...');
            
            const testData = {
                animal_type: 'cow',
                cow_weight: 500,
                cow_milk: 25,
                cow_stage: 'mid',
                activity_level: 'moderate'
            };
            
            fetch('/calculate_animal_requirements', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(testData)
            })
            .then(response => response.json())
            .then(data => {
                addToLog(`🧪 نتيجة الاختبار المباشر: ${JSON.stringify(data, null, 2)}`, 'success');
            })
            .catch(error => {
                addToLog(`❌ فشل الاختبار المباشر: ${error.message}`, 'error');
            });
        }
        
        // ربط الأحداث
        document.addEventListener('DOMContentLoaded', function() {
            addToLog('🚀 تم تحميل الصفحة بنجاح');
            
            const calculateBtn = document.getElementById('calculateBtn');
            const testApiBtn = document.getElementById('testApiBtn');
            
            if (calculateBtn) {
                calculateBtn.addEventListener('click', calculateRequirements);
                addToLog('✅ تم ربط زر حساب الاحتياجات');
            } else {
                addToLog('❌ لم يتم العثور على زر حساب الاحتياجات', 'error');
            }
            
            if (testApiBtn) {
                testApiBtn.addEventListener('click', testApiDirect);
                addToLog('✅ تم ربط زر اختبار API');
            }
        });
    </script>
</body>
</html>
