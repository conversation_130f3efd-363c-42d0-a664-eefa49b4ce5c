{% extends "base.html" %}

{% block title %}تاريخ حسابات الأبقار - برنامج تكوين الأعلاف المركزة{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        تاريخ حسابات الأبقار ({{ calculations|length }} سجل)
                    </h5>
                    <div class="btn-group">
                        <a href="{{ url_for('cow_requirements') }}" class="btn btn-light btn-sm">
                            <i class="fas fa-plus me-1"></i>
                            حساب جديد
                        </a>
                        <a href="{{ url_for('compare_cows') }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-balance-scale me-1"></i>
                            مقارنة الأبقار
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                {% if calculations %}
                <!-- فلاتر البحث -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <input type="text" class="form-control" id="searchCow" placeholder="البحث باسم البقرة...">
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filterStage">
                            <option value="">جميع المراحل</option>
                            <option value="early">إنتاج مبكر</option>
                            <option value="mid">إنتاج متوسط</option>
                            <option value="late">إنتاج متأخر</option>
                            <option value="dry">جافة</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="sortBy">
                            <option value="date_desc">الأحدث أولاً</option>
                            <option value="date_asc">الأقدم أولاً</option>
                            <option value="name_asc">اسم البقرة (أ-ي)</option>
                            <option value="cost_desc">التكلفة (عالي-منخفض)</option>
                            <option value="milk_desc">الإنتاج (عالي-منخفض)</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                            <i class="fas fa-times me-1"></i>
                            مسح
                        </button>
                    </div>
                </div>

                <!-- جدول السجلات -->
                <div class="table-responsive">
                    <table class="table table-hover" id="cowHistoryTable">
                        <thead class="table-dark">
                            <tr>
                                <th>اسم البقرة</th>
                                <th class="text-center">الوزن (كغ)</th>
                                <th class="text-center">الحليب (لتر/يوم)</th>
                                <th class="text-center">المرحلة</th>
                                <th class="text-center">العلف (كغ/يوم)</th>
                                <th class="text-center">البروتين (كغ)</th>
                                <th class="text-center">الطاقة (Mcal)</th>
                                <th class="text-center">التكلفة (د.أ/يوم)</th>
                                <th class="text-center">التاريخ</th>
                                <th class="text-center">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for calc in calculations %}
                            <tr class="cow-record" 
                                data-id="{{ calc.id }}"
                                data-name="{{ calc.cow_name|lower }}"
                                data-stage="{{ calc.lactation_stage }}"
                                data-date="{{ calc.calculation_date }}"
                                data-cost="{{ calc.daily_cost }}"
                                data-milk="{{ calc.milk_production }}">
                                <td>
                                    <strong class="text-primary">{{ calc.cow_name }}</strong>
                                    <br><small class="text-muted">ID: {{ calc.id }}</small>
                                </td>
                                <td class="text-center">{{ calc.weight }}</td>
                                <td class="text-center">
                                    <span class="badge bg-info">{{ calc.milk_production }}</span>
                                </td>
                                <td class="text-center">
                                    {% if calc.lactation_stage == 'early' %}
                                        <span class="badge bg-success">مبكر</span>
                                    {% elif calc.lactation_stage == 'mid' %}
                                        <span class="badge bg-primary">متوسط</span>
                                    {% elif calc.lactation_stage == 'late' %}
                                        <span class="badge bg-warning">متأخر</span>
                                    {% else %}
                                        <span class="badge bg-secondary">جافة</span>
                                    {% endif %}
                                </td>
                                <td class="text-center">{{ "%.1f"|format(calc.total_dm) }}</td>
                                <td class="text-center">{{ "%.2f"|format(calc.total_protein / 1000) }}</td>
                                <td class="text-center">{{ "%.1f"|format(calc.total_energy) }}</td>
                                <td class="text-center">
                                    <span class="text-success fw-bold">{{ "%.2f"|format(calc.daily_cost) }}</span>
                                </td>
                                <td class="text-center">
                                    <small>{{ calc.calculation_date.strftime('%Y-%m-%d') if calc.calculation_date else 'غير محدد' }}</small>
                                    <br><small class="text-muted">{{ calc.calculation_date.strftime('%H:%M') if calc.calculation_date else '' }}</small>
                                </td>
                                <td class="text-center">
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" 
                                                onclick="viewDetails({{ calc.id }})"
                                                data-bs-toggle="tooltip" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-success" 
                                                onclick="recalculate({{ calc.id }})"
                                                data-bs-toggle="tooltip" title="إعادة حساب">
                                            <i class="fas fa-calculator"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" 
                                                onclick="deleteRecord({{ calc.id }})"
                                                data-bs-toggle="tooltip" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="row mt-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white text-center">
                            <div class="card-body">
                                <h4>{{ calculations|length }}</h4>
                                <small>إجمالي السجلات</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white text-center">
                            <div class="card-body">
                                <h4 id="avgMilk">0</h4>
                                <small>متوسط الحليب (لتر)</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white text-center">
                            <div class="card-body">
                                <h4 id="avgCost">0</h4>
                                <small>متوسط التكلفة (د.أ)</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white text-center">
                            <div class="card-body">
                                <h4 id="totalCows">0</h4>
                                <small>عدد الأبقار المختلفة</small>
                            </div>
                        </div>
                    </div>
                </div>

                {% else %}
                <!-- لا توجد سجلات -->
                <div class="text-center py-5">
                    <i class="fas fa-cow fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد سجلات محفوظة</h4>
                    <p class="text-muted">ابدأ بحساب احتياجات البقرة الأولى</p>
                    <a href="{{ url_for('cow_requirements') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        حساب احتياجات بقرة جديدة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- نافذة عرض التفاصيل -->
<div class="modal fade" id="detailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">تفاصيل حسابات البقرة</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="detailsContent">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="printDetailsBtn">
                    <i class="fas fa-print me-1"></i>
                    طباعة
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let detailsModal = null;

document.addEventListener('DOMContentLoaded', function() {
    detailsModal = new bootstrap.Modal(document.getElementById('detailsModal'));
    
    // إعداد البحث والفلترة
    setupFiltering();
    
    // حساب الإحصائيات
    calculateStatistics();
    
    // تفعيل التولتيبس
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

function setupFiltering() {
    const searchInput = document.getElementById('searchCow');
    const stageFilter = document.getElementById('filterStage');
    const sortSelect = document.getElementById('sortBy');
    
    if (searchInput) {
        searchInput.addEventListener('input', filterRecords);
        stageFilter.addEventListener('change', filterRecords);
        sortSelect.addEventListener('change', sortRecords);
    }
}

function filterRecords() {
    const searchTerm = document.getElementById('searchCow').value.toLowerCase();
    const stageFilter = document.getElementById('filterStage').value;
    const rows = document.querySelectorAll('.cow-record');
    
    rows.forEach(row => {
        const name = row.dataset.name;
        const stage = row.dataset.stage;
        
        const matchesSearch = name.includes(searchTerm);
        const matchesStage = !stageFilter || stage === stageFilter;
        
        if (matchesSearch && matchesStage) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function sortRecords() {
    const sortBy = document.getElementById('sortBy').value;
    const table = document.getElementById('cowHistoryTable');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('.cow-record'));
    
    rows.sort((a, b) => {
        switch(sortBy) {
            case 'date_desc':
                return new Date(b.dataset.date) - new Date(a.dataset.date);
            case 'date_asc':
                return new Date(a.dataset.date) - new Date(b.dataset.date);
            case 'name_asc':
                return a.dataset.name.localeCompare(b.dataset.name);
            case 'cost_desc':
                return parseFloat(b.dataset.cost) - parseFloat(a.dataset.cost);
            case 'milk_desc':
                return parseFloat(b.dataset.milk) - parseFloat(a.dataset.milk);
            default:
                return 0;
        }
    });
    
    rows.forEach(row => tbody.appendChild(row));
}

function clearFilters() {
    document.getElementById('searchCow').value = '';
    document.getElementById('filterStage').value = '';
    document.getElementById('sortBy').value = 'date_desc';
    filterRecords();
    sortRecords();
}

function calculateStatistics() {
    const rows = document.querySelectorAll('.cow-record');
    if (rows.length === 0) return;
    
    let totalMilk = 0;
    let totalCost = 0;
    const uniqueCows = new Set();
    
    rows.forEach(row => {
        totalMilk += parseFloat(row.dataset.milk);
        totalCost += parseFloat(row.dataset.cost);
        uniqueCows.add(row.dataset.name);
    });
    
    const avgMilk = totalMilk / rows.length;
    const avgCost = totalCost / rows.length;
    
    document.getElementById('avgMilk').textContent = avgMilk.toFixed(1);
    document.getElementById('avgCost').textContent = avgCost.toFixed(2);
    document.getElementById('totalCows').textContent = uniqueCows.size;
}

function viewDetails(calcId) {
    fetch(`/cow_calculation/${calcId}`)
        .then(response => response.json())
        .then(data => {
            const content = `
                <div class="alert alert-info">
                    <strong>معرف الحساب:</strong> ${data.id}<br>
                    <strong>اسم البقرة:</strong> ${data.cow_name}<br>
                    <strong>تاريخ الحساب:</strong> ${new Date(data.calculation_date).toLocaleString()}
                </div>
                <table class="table table-bordered">
                    <tbody>
                        <tr><th>الوزن</th><td>${data.weight} كغ</td></tr>
                        <tr><th>إنتاج الحليب</th><td>${data.milk_production} لتر/يوم</td></tr>
                        <tr><th>مرحلة الإنتاج</th><td>${data.lactation_stage}</td></tr>
                        <tr><th>مستوى النشاط</th><td>${data.activity_level}</td></tr>
                        <tr><th>إجمالي المادة الجافة</th><td>${data.total_dm.toFixed(2)} كغ</td></tr>
                        <tr><th>إجمالي البروتين</th><td>${(data.total_protein / 1000).toFixed(2)} كغ</td></tr>
                        <tr><th>إجمالي الطاقة</th><td>${data.total_energy.toFixed(2)} Mcal</td></tr>
                        <tr><th>التكلفة اليومية</th><td>${data.daily_cost.toFixed(2)} د.أ</td></tr>
                    </tbody>
                </table>
            `;
            document.getElementById('detailsContent').innerHTML = content;
            detailsModal.show();
        })
        .catch(error => {
            console.error('Error fetching details:', error);
            alert('حدث خطأ أثناء جلب التفاصيل.');
        });
}

function recalculate(calcId) {
    if (confirm('هل تريد إعادة حساب احتياجات هذه البقرة بنفس البيانات؟')) {
        window.location.href = `/recalculate_cow/${calcId}`;
    }
}

function deleteRecord(calcId) {
    if (confirm('هل أنت متأكد من حذف هذا السجل؟ لا يمكن التراجع عن هذا الإجراء.')) {
        fetch(`/delete_cow_calculation/${calcId}`, { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    // Reload the page to reflect changes
                    location.reload();
                } else {
                    alert('خطأ: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error deleting record:', error);
                alert('حدث خطأ أثناء حذف السجل.');
            });
    }
}

document.getElementById('printDetailsBtn').addEventListener('click', function() {
    window.print();
});
</script>
{% endblock %}