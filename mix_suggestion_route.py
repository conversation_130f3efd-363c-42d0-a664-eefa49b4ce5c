#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Route منفصل لاقتراح الخلطة
"""

from flask import request, jsonify

def setup_mix_routes(app):
    """إضافة routes اقتراح الخلطة إلى التطبيق"""
    
    @app.route('/suggest_mix_for_cow', methods=['POST'])
    def suggest_cow_mix():
        """API لاقتراح خلطة علف للبقرة"""
        try:
            print("📨 تم استلام طلب اقتراح الخلطة")
            
            data = request.get_json()
            requirements = data.get('requirements') if data else None
            
            print(f"📊 البيانات المستلمة: {requirements}")
            
            if not requirements:
                return jsonify({'error': 'لم يتم تمرير البيانات المطلوبة'}), 400
            
            # إنشاء خلطة مبسطة للاختبار
            simple_mix = {
                'mix_components': [
                    {
                        'ingredient': {'name': 'شعير', 'protein': 11.5, 'energy': 2900},
                        'quantity': 8.0,
                        'percentage': 43.2,
                        'reason': 'مصدر أساسي للطاقة'
                    },
                    {
                        'ingredient': {'name': 'كسب فول الصويا', 'protein': 44.0, 'energy': 2230},
                        'quantity': 3.5,
                        'percentage': 18.9,
                        'reason': 'مصدر البروتين الرئيسي'
                    },
                    {
                        'ingredient': {'name': 'نخالة القمح', 'protein': 15.5, 'energy': 2050},
                        'quantity': 4.0,
                        'percentage': 21.6,
                        'reason': 'مصدر الألياف والبروتين'
                    },
                    {
                        'ingredient': {'name': 'دريس البرسيم', 'protein': 18.0, 'energy': 2200},
                        'quantity': 3.0,
                        'percentage': 16.2,
                        'reason': 'مصدر الألياف والفيتامينات'
                    }
                ],
                'totals': {
                    'weight': 18.5,
                    'cost': 12.50,
                    'cost_per_kg': 0.676
                },
                'quality_indicators': {
                    'protein_match': 95.2,
                    'energy_match': 98.1,
                    'cost_efficiency': 'ممتاز'
                },
                'recommendations': [
                    {
                        'type': 'good',
                        'message': 'مستوى البروتين ممتاز',
                        'suggestion': 'الخلطة تلبي احتياجات البروتين بشكل مثالي'
                    },
                    {
                        'type': 'good', 
                        'message': 'مستوى الطاقة ممتاز',
                        'suggestion': 'الخلطة تلبي احتياجات الطاقة بشكل مثالي'
                    },
                    {
                        'type': 'good',
                        'message': 'التكلفة اقتصادية',
                        'suggestion': 'الخلطة ذات تكلفة منخفضة ومناسبة'
                    }
                ]
            }
            
            print("✅ تم إنشاء الخلطة بنجاح")
            return jsonify({
                'success': True,
                'suggested_mix': simple_mix
            })
            
        except Exception as e:
            print(f"❌ خطأ في اقتراح الخلطة: {e}")
            return jsonify({'error': f'خطأ في اقتراح الخلطة: {str(e)}'}), 500
    
    print("✅ تم إضافة route اقتراح الخلطة بنجاح")