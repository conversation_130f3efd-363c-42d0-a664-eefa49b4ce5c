#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time

def test_mix_display():
    """اختبار عرض الخلطة في البطاقة"""
    
    base_url = "http://localhost:5000"
    
    print("🧪 اختبار عرض الخلطة في البطاقة...")
    
    # بيانات احتياجات البقرة للاختبار
    requirements = {
        "total_dm": 24.26,
        "total_protein": 1882,
        "total_energy": 28.49,
        "protein_percentage": 7.8,
        "daily_cost": 9.645
    }
    
    print(f"📊 الاحتياجات: {requirements}")
    
    try:
        # اختبار endpoint اقتراح الخلطة
        print("\n1. اختبار endpoint اقتراح الخلطة...")
        response = requests.post(f"{base_url}/suggest_mix_for_cow", 
                               json={"requirements": requirements})
        
        print(f"   كود الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                print("   ✅ نجح اقتراح الخلطة!")
                
                suggested_mix = data['suggested_mix']
                
                # عرض ملخص سريع
                print(f"   📋 عدد المكونات: {len(suggested_mix['mix_components'])}")
                print(f"   💰 إجمالي التكلفة: {suggested_mix['totals']['cost']} ريال")
                print(f"   📊 دقة البروتين: {suggested_mix['quality_indicators']['protein_match']}%")
                print(f"   📊 دقة الطاقة: {suggested_mix['quality_indicators']['energy_match']}%")
                
                return True
            else:
                print(f"   ❌ فشل في اقتراح الخلطة: {data.get('error', 'خطأ غير محدد')}")
                return False
        else:
            print(f"   ❌ فشل في الطلب: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def test_page_access():
    """اختبار الوصول لصفحة احتياجات البقرة"""
    
    base_url = "http://localhost:5000"
    
    print("\n2. اختبار الوصول لصفحة احتياجات البقرة...")
    
    try:
        response = requests.get(f"{base_url}/cow_requirements")
        
        if response.status_code == 200:
            print("   ✅ الصفحة تعمل بشكل صحيح")
            
            # التحقق من وجود العناصر المهمة في الصفحة
            content = response.text
            
            checks = [
                ("suggestMixBtn", "زر اقتراح الخلطة"),
                ("suggestedMixCard", "بطاقة الخلطة المقترحة"),
                ("acceptMixBtn", "زر قبول الخلطة"),
                ("modifyMixBtn", "زر تعديل الخلطة"),
                ("mixComponentsTable", "جدول مكونات الخلطة")
            ]
            
            for element_id, description in checks:
                if element_id in content:
                    print(f"   ✅ {description} موجود")
                else:
                    print(f"   ❌ {description} مفقود")
            
            return True
        else:
            print(f"   ❌ فشل في الوصول للصفحة: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في الوصول للصفحة: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار عرض الخلطة...")
    print("=" * 50)
    
    # اختبار الوصول للصفحة
    page_ok = test_page_access()
    
    # اختبار اقتراح الخلطة
    mix_ok = test_mix_display()
    
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    print(f"   الصفحة: {'✅ تعمل' if page_ok else '❌ لا تعمل'}")
    print(f"   اقتراح الخلطة: {'✅ يعمل' if mix_ok else '❌ لا يعمل'}")
    
    if page_ok and mix_ok:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("💡 يمكنك الآن فتح الصفحة واختبار الوظيفة:")
        print("   http://localhost:5000/cow_requirements")
    else:
        print("\n⚠️ هناك مشاكل تحتاج إصلاح")
