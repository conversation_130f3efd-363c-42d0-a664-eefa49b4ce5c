# برنامج تكوين الأعلاف المركزة

## وصف البرنامج
برنامج متكامل لتكوين وحساب خلطات الأعلاف المركزة مع إمكانية حساب:
- نسبة البروتين الإجمالي
- الطاقة الإجمالية  
- نسبة الألياف
- التكلفة الإجمالية بالدينار الأردني

## المميزات الرئيسية
✅ **واجهة عربية كاملة** مع دعم الكتابة من اليمين لليسار  
✅ **إضافة وإدارة المكونات** مع البيانات الغذائية والأسعار  
✅ **حساب دقيق للخلطات** مع عرض تفصيلي للنتائج  
✅ **حفظ واسترجاع الخلطات** مع قاعدة بيانات محلية  
✅ **تقارير قابلة للطباعة** مع تفاصيل شاملة  
✅ **واجهة متجاوبة** تعمل على جميع الأجهزة  

## متطلبات النظام
- Python 3.7 أو أحدث
- Flask و SQLAlchemy
- متصفح ويب حديث

## طريقة التشغيل

### 1. تثبيت Python
تأكد من تثبيت Python 3.7+ على جهازك

### 2. تثبيت المكتبات المطلوبة
```bash
pip install -r requirements.txt
```

### 3. تشغيل البرنامج
```bash
python app.py
```

### 4. فتح المتصفح
افتح المتصفح وانتقل إلى:
```
http://localhost:5000
```

## استخدام البرنامج

### إضافة مكون جديد
1. في القسم الأيسر، أدخل بيانات المكون:
   - اسم المكون
   - نسبة البروتين (%)
   - الطاقة (kcal/kg)
   - نسبة الألياف (%)
   - السعر للكيلوغرام بالدينار الأردني

### تكوين خلطة جديدة
1. في الجدول الرئيسي، أدخل الكميات المطلوبة لكل مكون
2. اضغط على "حساب الخلطة" لعرض النتائج
3. يمكنك حفظ الخلطة بإعطائها اسماً مميزاً

### عرض الخلطات المحفوظة
1. انتقل إلى صفحة "الخلطات المحفوظة"
2. يمكنك عرض تفاصيل أي خلطة أو حذفها
3. يمكن طباعة تقرير مفصل لكل خلطة

## المعادلات المستخدمة

### حساب نسبة العنصر الغذائي:
```
النسبة = (كمية المكون × نسبة العنصر في المكون) / إجمالي وزن الخلطة × 100
```

### حساب التكلفة الإجمالية:
```
التكلفة = مجموع (كمية المكون × سعر الكغ)
```

## هيكل المشروع
```
feed_mixer/
├── app.py                 # الملف الرئيسي للتطبيق
├── requirements.txt       # المكتبات المطلوبة
├── README.md             # دليل الاستخدام
├── templates/            # قوالب HTML
│   ├── base.html         # القالب الأساسي
│   ├── index.html        # الصفحة الرئيسية
│   ├── saved_mixes.html  # صفحة الخلطات المحفوظة
│   └── view_mix.html     # صفحة عرض تفاصيل الخلطة
├── static/               # الملفات الثابتة
│   ├── css/
│   │   └── style.css     # تنسيقات CSS
│   └── js/
│       └── main.js       # تفاعلات JavaScript
└── feed_mixer.db         # قاعدة البيانات (تُنشأ تلقائياً)
```

## قاعدة البيانات

### جدول المكونات (ingredients)
| العمود | النوع | الوصف |
|--------|-------|-------|
| id | Integer | المعرف الفريد |
| name | String | اسم المكون |
| protein | Float | نسبة البروتين % |
| energy | Float | الطاقة kcal/kg |
| fiber | Float | نسبة الألياف % |
| price_per_kg | Float | السعر/كغ بالدينار |

### جدول الخلطات (mixes)
| العمود | النوع | الوصف |
|--------|-------|-------|
| id | Integer | المعرف الفريد |
| name | String | اسم الخلطة |
| date_created | DateTime | تاريخ الإنشاء |
| total_weight | Float | الوزن الإجمالي |
| total_cost | Float | التكلفة الإجمالية |
| avg_protein | Float | متوسط البروتين |
| avg_energy | Float | متوسط الطاقة |
| avg_fiber | Float | متوسط الألياف |

### جدول تفاصيل الخلطة (mix_details)
| العمود | النوع | الوصف |
|--------|-------|-------|
| id | Integer | المعرف الفريد |
| mix_id | Integer | معرف الخلطة |
| ingredient_id | Integer | معرف المكون |
| quantity_kg | Float | الكمية بالكيلوغرام |

## البيانات التجريبية
يحتوي البرنامج على بيانات تجريبية للمكونات التالية:
- ذرة صفراء
- كسبة فول الصويا  
- شعير
- نخالة قمح
- كربونات كالسيوم
- ملح طعام

## الدعم والمساعدة
- للأسئلة التقنية: راجع كود المصدر في `app.py`
- لمشاكل الواجهة: راجع ملفات `templates/` و `static/`
- للتخصيص: يمكن تعديل الألوان والخطوط في `static/css/style.css`

## الترخيص
هذا البرنامج مفتوح المصدر ويمكن استخدامه وتطويره بحرية.

---
**تم تطوير هذا البرنامج لمساعدة المزارعين ومربي الحيوانات في تكوين خلطات علف متوازنة واقتصادية.**