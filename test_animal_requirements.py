#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_animal_requirements():
    """اختبار حساب احتياجات الحيوانات"""
    
    url = "http://localhost:5000/calculate_animal_requirements"
    
    # اختبار البقرة
    cow_data = {
        "animal_type": "cow",
        "cow_weight": 550,
        "cow_milk": 25,
        "cow_stage": "mid",
        "activity_level": "moderate"
    }
    
    print("🐄 اختبار حساب احتياجات البقرة...")
    print(f"البيانات المرسلة: {cow_data}")
    
    try:
        response = requests.post(url, json=cow_data)
        print(f"كود الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ نجح الحساب!")
            print(f"المادة الجافة: {result.get('total_dm', 'غير محدد')} كغ/يوم")
            print(f"الطاقة: {result.get('total_energy', 'غير محدد')} Mcal/يوم")
            print(f"البروتين: {result.get('total_protein', 'غير محدد')} غرام/يوم")
            print(f"نسبة البروتين: {result.get('protein_percentage', 'غير محدد')}%")
            print(f"التكلفة اليومية: {result.get('daily_cost', 'غير محدد')} ريال")
        else:
            print(f"❌ فشل الحساب: {response.text}")
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
    
    # اختبار العجل
    print("\n🐮 اختبار حساب احتياجات العجل...")
    calf_data = {
        "animal_type": "calf",
        "calf_weight": 150,
        "calf_age": 6,
        "calf_growth": 0.8,
        "calf_type": "dairy"
    }
    
    try:
        response = requests.post(url, json=calf_data)
        if response.status_code == 200:
            result = response.json()
            print("✅ نجح حساب احتياجات العجل!")
            print(f"المادة الجافة: {result.get('total_dm', 'غير محدد')} كغ/يوم")
        else:
            print(f"❌ فشل حساب العجل: {response.text}")
    except Exception as e:
        print(f"❌ خطأ في حساب العجل: {e}")

if __name__ == "__main__":
    test_animal_requirements()
