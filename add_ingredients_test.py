#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار وإضافة مكونات تجريبية لاختبار اقتراح الخلطة
"""

from app import app, db, Ingredient

def add_test_ingredients():
    """إضافة مكونات تجريبية لاختبار النظام"""
    
    with app.app_context():
        print("🧪 اختبار وإضافة المكونات التجريبية...")
        
        # التحقق من وجود مكونات
        existing_count = Ingredient.query.count()
        print(f"📊 عدد المكونات الموجودة: {existing_count}")
        
        if existing_count > 0:
            print("✅ يوجد مكونات في قاعدة البيانات")
            ingredients = Ingredient.query.all()
            for ing in ingredients:
                print(f"  - {ing.name}: بروتين {ing.protein}%, طاقة {ing.energy} kcal/kg, سعر {ing.price_per_kg} د.أ/كغ")
            return
        
        print("🔄 إضافة مكونات تجريبية...")
        
        # مكونات أساسية للعلف
        test_ingredients = [
            {
                'name': 'شعير',
                'protein': 11.5,
                'energy': 2900,
                'fiber': 5.5,
                'price_per_kg': 0.35
            },
            {
                'name': 'ذرة صفراء',
                'protein': 8.8,
                'energy': 3350,
                'fiber': 2.3,
                'price_per_kg': 0.42
            },
            {
                'name': 'كسب فول الصويا',
                'protein': 44.0,
                'energy': 2230,
                'fiber': 7.0,
                'price_per_kg': 1.20
            },
            {
                'name': 'نخالة القمح',
                'protein': 15.5,
                'energy': 2050,
                'fiber': 12.0,
                'price_per_kg': 0.28
            },
            {
                'name': 'كسب عباد الشمس',
                'protein': 28.0,
                'energy': 2100,
                'fiber': 20.0,
                'price_per_kg': 0.65
            },
            {
                'name': 'دريس البرسيم',
                'protein': 18.0,
                'energy': 2200,
                'fiber': 25.0,
                'price_per_kg': 0.45
            },
            {
                'name': 'قش القمح',
                'protein': 3.5,
                'energy': 1800,
                'fiber': 40.0,
                'price_per_kg': 0.15
            },
            {
                'name': 'مولاس',
                'protein': 3.0,
                'energy': 2800,
                'fiber': 0.0,
                'price_per_kg': 0.50
            },
            {
                'name': 'ملح الطعام',
                'protein': 0.0,
                'energy': 0,
                'fiber': 0.0,
                'price_per_kg': 0.80
            },
            {
                'name': 'حجر الكلس',
                'protein': 0.0,
                'energy': 0,
                'fiber': 0.0,
                'price_per_kg': 0.25
            }
        ]
        
        # إضافة المكونات
        for ing_data in test_ingredients:
            try:
                # التحقق من عدم وجود المكون
                existing = Ingredient.query.filter_by(name=ing_data['name']).first()
                if existing:
                    print(f"  ⚠️ {ing_data['name']} موجود مسبقاً")
                    continue
                
                ingredient = Ingredient(
                    name=ing_data['name'],
                    protein=ing_data['protein'],
                    energy=ing_data['energy'],
                    fiber=ing_data['fiber'],
                    price_per_kg=ing_data['price_per_kg']
                )
                
                db.session.add(ingredient)
                print(f"  ✅ تم إضافة: {ing_data['name']}")
                
            except Exception as e:
                print(f"  ❌ خطأ في إضافة {ing_data['name']}: {e}")
        
        try:
            db.session.commit()
            print("💾 تم حفظ جميع المكونات بنجاح!")
            
            # عرض النتيجة النهائية
            final_count = Ingredient.query.count()
            print(f"📊 العدد النهائي للمكونات: {final_count}")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ خطأ في حفظ المكونات: {e}")

if __name__ == '__main__':
    print("🌾 برنامج إضافة المكونات التجريبية")
    print("=" * 50)
    add_test_ingredients()
    print("=" * 50)
    print("🎉 انتهى البرنامج!")