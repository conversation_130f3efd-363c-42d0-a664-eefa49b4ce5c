# -*- mode: python ; coding: utf-8 -*-

import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# جمع ملفات Flask
flask_data = collect_data_files('flask')

# جمع ملفات Jinja2
jinja2_data = collect_data_files('jinja2')

# جمع ملفات إضافية
added_files = [
    ('templates', 'templates'),
    ('static', 'static'),
]

# جمع مكتبات إضافية
hidden_imports = [
    'flask',
    'jinja2',
    'sqlite3',
    'json',
    'datetime',
    'os',
    'sys',
    'webbrowser',
    'threading',
    'time'
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=added_files + flask_data + jinja2_data,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='برنامج_تكوين_الاعلاف',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='app_icon.ico',
    version_info={
        'version': '1.0.0',
        'description': 'برنامج تكوين الأعلاف المركزة - Feed Formulation System',
        'product_name': 'Feed Mixer Pro',
        'file_description': 'نظام تكوين الأعلاف المركزة للمواشي',
        'company_name': 'Agricultural Solutions',
        'copyright': '© 2024 جميع الحقوق محفوظة',
    }
)