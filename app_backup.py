from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import json

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///feed_mixer.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

# نماذج قاعدة البيانات
class Ingredient(db.Model):
    """جدول المكونات"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    protein = db.Column(db.Float, nullable=False)  # نسبة البروتين %
    energy = db.Column(db.Float, nullable=False)   # الطاقة kcal/kg
    fiber = db.Column(db.Float, nullable=False)    # نسبة الألياف %
    price_per_kg = db.Column(db.Float, nullable=False)  # السعر/كغ بالدينار الأردني
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'protein': self.protein,
            'energy': self.energy,
            'fiber': self.fiber,
            'price_per_kg': self.price_per_kg
        }

class Mix(db.Model):
    """جدول الخلطات"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    date_created = db.Column(db.DateTime, default=datetime.utcnow)
    total_weight = db.Column(db.Float, nullable=False)
    total_cost = db.Column(db.Float, nullable=False)
    avg_protein = db.Column(db.Float, nullable=False)
    avg_energy = db.Column(db.Float, nullable=False)
    avg_fiber = db.Column(db.Float, nullable=False)
    
    # العلاقة مع تفاصيل الخلطة
    details = db.relationship('MixDetail', backref='mix', lazy=True, cascade='all, delete-orphan')

class MixDetail(db.Model):
    """جدول تفاصيل الخلطة"""
    id = db.Column(db.Integer, primary_key=True)
    mix_id = db.Column(db.Integer, db.ForeignKey('mix.id'), nullable=False)
    ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredient.id'), nullable=False)
    quantity_kg = db.Column(db.Float, nullable=False)  # الكمية بالكيلوغرام
    
    # العلاقة مع المكون
    ingredient = db.relationship('Ingredient', backref='mix_details')

# إنشاء قاعدة البيانات
with app.app_context():
    db.create_all()
    
    # إضافة بيانات تجريبية إذا لم تكن موجودة
    if Ingredient.query.count() == 0:
        sample_ingredients = [
            Ingredient(name='ذرة صفراء', protein=8.5, energy=3350, fiber=2.3, price_per_kg=0.45),
            Ingredient(name='كسبة فول الصويا', protein=44.0, energy=2230, fiber=7.0, price_per_kg=0.65),
            Ingredient(name='شعير', protein=11.5, energy=2800, fiber=5.5, price_per_kg=0.38),
            Ingredient(name='نخالة قمح', protein=15.5, energy=1260, fiber=10.0, price_per_kg=0.25),
            Ingredient(name='كربونات كالسيوم', protein=0.0, energy=0, fiber=0.0, price_per_kg=0.15),
            Ingredient(name='ملح طعام', protein=0.0, energy=0, fiber=0.0, price_per_kg=0.20),
        ]
        
        for ingredient in sample_ingredients:
            db.session.add(ingredient)
        db.session.commit()

class CowCalculation(db.Model):
    """جدول حسابات الأبقار"""
    id = db.Column(db.Integer, primary_key=True)
    cow_name = db.Column(db.String(100), nullable=False, default='بقرة')
    weight = db.Column(db.Float, nullable=False)
    milk_production = db.Column(db.Float, nullable=False)
    lactation_stage = db.Column(db.String(50), nullable=False)
    activity_level = db.Column(db.String(50), nullable=False)
    calculation_date = db.Column(db.DateTime, default=datetime.utcnow)
    
    # الاحتياجات المحسوبة
    total_dm = db.Column(db.Float, nullable=False)
    total_protein = db.Column(db.Float, nullable=False)
    total_energy = db.Column(db.Float, nullable=False)
    
    # الخلطة المقترحة
    suggested_mix = db.Column(db.Text, nullable=True) # JSON string
    daily_cost = db.Column(db.Float, nullable=True)

# ... (الكود الحالي)

@app.route('/animal_requirements')
def animal_requirements():
    """صفحة حساب احتياجات جميع الحيوانات"""
    return render_template('animal_requirements.html', title="احتياجات الحيوانات")

@app.route('/compare_cows')
def compare_cows():
    """صفحة مقارنة الأبقار"""
    calculations = CowCalculation.query.order_by(CowCalculation.calculation_date.desc()).all()
    return render_template('compare_cows_simple.html', calculations=calculations)





@app.route('/')
def index():
    """الصفحة الرئيسية"""
    ingredients = Ingredient.query.all()
    return render_template('index.html', ingredients=ingredients)

@app.route('/cow_requirements')
def cow_requirements():
    """صفحة حساب احتياجات البقرة"""
    # Get data from query parameters for recalculation
    weight = request.args.get('weight', '')
    milk = request.args.get('milk', '')
    stage = request.args.get('stage', 'mid')
    activity = request.args.get('activity', 'moderate')
    
    return render_template('cow_requirements.html', 
                           weight=weight, 
                           milk=milk, 
                           stage=stage, 
                           activity=activity)

@app.route('/add_ingredient', methods=['POST'])
def add_ingredient():
    """إضافة مكون جديد"""
    try:
        name = request.form['name']
        protein = float(request.form['protein'])
        energy = float(request.form['energy'])
        fiber = float(request.form['fiber'])
        price_per_kg = float(request.form['price_per_kg'])
        
        new_ingredient = Ingredient(
            name=name,
            protein=protein,
            energy=energy,
            fiber=fiber,
            price_per_kg=price_per_kg
        )
        
        db.session.add(new_ingredient)
        db.session.commit()
        
        flash('تم إضافة المكون بنجاح!', 'success')
    except Exception as e:
        flash(f'خطأ في إضافة المكون: {str(e)}', 'error')
    
    return redirect(url_for('index'))

@app.route('/calculate_mix', methods=['POST'])
def calculate_mix():
    """حساب خصائص الخلطة"""
    try:
        data = request.get_json()
        ingredients_data = data['ingredients']
        
        total_weight = 0
        total_cost = 0
        total_protein_weight = 0
        total_energy_weight = 0
        total_fiber_weight = 0
        
        results = []
        
        for item in ingredients_data:
            ingredient_id = item['id']
            quantity = float(item['quantity'])
            
            if quantity <= 0:
                continue
                
            ingredient = Ingredient.query.get(ingredient_id)
            if not ingredient:
                continue
            
            # حساب التكلفة والقيم الغذائية
            cost = quantity * ingredient.price_per_kg
            protein_contribution = (quantity * ingredient.protein) / 100
            energy_contribution = quantity * ingredient.energy
            fiber_contribution = (quantity * ingredient.fiber) / 100
            
            total_weight += quantity
            total_cost += cost
            total_protein_weight += protein_contribution
            total_energy_weight += energy_contribution
            total_fiber_weight += fiber_contribution
            
            results.append({
                'name': ingredient.name,
                'quantity': quantity,
                'cost': round(cost, 3),
                'protein_contribution': round(protein_contribution, 2),
                'energy_contribution': round(energy_contribution, 2),
                'fiber_contribution': round(fiber_contribution, 2)
            })
        
        if total_weight == 0:
            return jsonify({'error': 'لا توجد كميات محددة'})
        
        # حساب المتوسطات
        avg_protein = (total_protein_weight / total_weight) * 100
        avg_energy = total_energy_weight / total_weight
        avg_fiber = (total_fiber_weight / total_weight) * 100
        
        return jsonify({
            'success': True,
            'total_weight': round(total_weight, 2),
            'total_cost': round(total_cost, 3),
            'avg_protein': round(avg_protein, 2),
            'avg_energy': round(avg_energy, 2),
            'avg_fiber': round(avg_fiber, 2),
            'cost_per_kg': round(total_cost / total_weight, 3),
            'details': results
        })
        
    except Exception as e:
        return jsonify({'error': f'خطأ في الحساب: {str(e)}'})

@app.route('/save_mix', methods=['POST'])
def save_mix():
    """حفظ الخلطة"""
    try:
        data = request.get_json()
        mix_name = data['name']
        ingredients_data = data['ingredients']
        calculations = data['calculations']
        
        # إنشاء خلطة جديدة
        new_mix = Mix(
            name=mix_name,
            total_weight=calculations['total_weight'],
            total_cost=calculations['total_cost'],
            avg_protein=calculations['avg_protein'],
            avg_energy=calculations['avg_energy'],
            avg_fiber=calculations['avg_fiber']
        )
        
        db.session.add(new_mix)
        db.session.flush()  # للحصول على ID الخلطة
        
        # إضافة تفاصيل الخلطة
        for item in ingredients_data:
            if float(item['quantity']) > 0:
                detail = MixDetail(
                    mix_id=new_mix.id,
                    ingredient_id=item['id'],
                    quantity_kg=float(item['quantity'])
                )
                db.session.add(detail)
        
        db.session.commit()
        
        return jsonify({'success': True, 'message': 'تم حفظ الخلطة بنجاح!'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'خطأ في حفظ الخلطة: {str(e)}'})

@app.route('/saved_mixes')
def saved_mixes():
    """عرض الخلطات المحفوظة"""
    mixes = Mix.query.order_by(Mix.date_created.desc()).all()
    return render_template('saved_mixes.html', mixes=mixes)

@app.route('/view_mix/<int:mix_id>')
def view_mix(mix_id):
    """عرض تفاصيل خلطة محددة"""
    mix = Mix.query.get_or_404(mix_id)
    return render_template('view_mix.html', mix=mix)

@app.route('/delete_mix/<int:mix_id>', methods=['POST'])
def delete_mix(mix_id):
    """حذف خلطة"""
    try:
        mix = Mix.query.get_or_404(mix_id)
        db.session.delete(mix)
        db.session.commit()
        flash('تم حذف الخلطة بنجاح!', 'success')
    except Exception as e:
        flash(f'خطأ في حذف الخلطة: {str(e)}', 'error')
    
    return redirect(url_for('saved_mixes'))

@app.route('/api/ingredients')
def api_ingredients():
    """API لجلب جميع المكونات"""
    ingredients = Ingredient.query.all()
    return jsonify([ingredient.to_dict() for ingredient in ingredients])

@app.route('/suggest_mix_for_cow', methods=['POST'])
def suggest_mix_for_cow():
    """API لاقتراح خلطة بقرة"""
    try:
        data = request.get_json()
        requirements = data['requirements']
        
        # Implement your mix suggestion logic here based on requirements
        # For now, returning a dummy response
        
        suggested_mix = {
            "totals": {
                "weight": 10,
                "cost": 5,
                "cost_per_kg": 0.5
            },
            "quality_indicators": {
                "protein_match": "95%",
                "energy_match": "98%",
                "cost_efficiency": "High"
            },
            "mix_components": [
                {
                    "ingredient": {"name": "Corn", "protein": 8.5, "energy": 3350, "price_per_kg": 0.45},
                    "quantity": 5,
                    "percentage": 50,
                    "reason": "Energy source"
                },
                {
                    "ingredient": {"name": "Soybean Meal", "protein": 44, "energy": 2230, "price_per_kg": 0.65},
                    "quantity": 5,
                    "percentage": 50,
                    "reason": "Protein source"
                }
            ],
            "recommendations": [
                {"type": "good", "message": "Well balanced mix", "suggestion": "No changes needed"}
            ]
        }
        
        return jsonify({'suggested_mix': suggested_mix})
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/save_cow_calculation', methods=['POST'])
def save_cow_calculation():
    """API لحفظ حسابات البقرة"""
    try:
        data = request.get_json()
        
        new_calculation = CowCalculation(
            cow_name=data['cow_name'],
            weight=data['weight'],
            milk_production=data['milk_production'],
            lactation_stage=data['lactation_stage'],
            activity_level=data['activity_level'],
            total_dm=data['requirements']['totalDM'],
            total_protein=data['requirements']['totalProtein'],
            total_energy=data['requirements']['totalEnergy'],
            daily_cost=data['requirements']['dailyCost']
        )
        
        db.session.add(new_calculation)
        db.session.commit()
        
        return jsonify({'message': 'تم حفظ حسابات البقرة بنجاح!'})
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/cow_calculation/<int:calc_id>')
def get_cow_calculation(calc_id):
    """API لجلب تفاصيل حساب بقرة"""
    calc = CowCalculation.query.get_or_404(calc_id)
    return jsonify({
        'id': calc.id,
        'cow_name': calc.cow_name,
        'weight': calc.weight,
        'milk_production': calc.milk_production,
        'lactation_stage': calc.lactation_stage,
        'activity_level': calc.activity_level,
        'calculation_date': calc.calculation_date.isoformat(),
        'total_dm': calc.total_dm,
        'total_protein': calc.total_protein,
        'total_energy': calc.total_energy,
        'daily_cost': calc.daily_cost
    })

@app.route('/delete_cow_calculation/<int:calc_id>', methods=['POST'])
def delete_cow_calculation(calc_id):
    """API لحذف حساب بقرة"""
    try:
        calc = CowCalculation.query.get_or_404(calc_id)
        db.session.delete(calc)
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم حذف السجل بنجاح'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/recalculate_cow/<int:calc_id>')
def recalculate_cow(calc_id):
    """إعادة حساب بقرة"""
    calc = CowCalculation.query.get_or_404(calc_id)
    # Redirect to cow_requirements page with query parameters
    return redirect(url_for('cow_requirements', 
                            weight=calc.weight, 
                            milk=calc.milk_production, 
                            stage=calc.lactation_stage, 
                            activity=calc.activity_level))

@app.route('/cow_history')
def cow_history():
    """صفحة تاريخ حسابات الأبقار"""
    calculations = CowCalculation.query.order_by(CowCalculation.calculation_date.desc()).all()
    return render_template('cow_history.html', calculations=calculations)

@app.route('/manage_ingredients')
def manage_ingredients():
    """صفحة إدارة المكونات"""
    ingredients = Ingredient.query.all()
    return render_template('manage_ingredients.html', ingredients=ingredients)

@app.route('/edit_ingredient/<int:ingredient_id>', methods=['POST'])
def edit_ingredient(ingredient_id):
    """API لتعديل مكون"""
    try:
        ingredient = Ingredient.query.get_or_404(ingredient_id)
        ingredient.name = request.form['name']
        ingredient.protein = float(request.form['protein'])
        ingredient.energy = float(request.form['energy'])
        ingredient.fiber = float(request.form['fiber'])
        ingredient.price_per_kg = float(request.form['price_per_kg'])
        db.session.commit()
        flash('تم تعديل المكون بنجاح!', 'success')
    except Exception as e:
        flash(f'خطأ في تعديل المكون: {str(e)}', 'error')
    return redirect(url_for('manage_ingredients'))

@app.route('/delete_ingredient/<int:ingredient_id>', methods=['POST'])
def delete_ingredient(ingredient_id):
    """API لحذف مكون"""
    try:
        ingredient = Ingredient.query.get_or_404(ingredient_id)
        db.session.delete(ingredient)
        db.session.commit()
        flash('تم حذف المكون بنجاح!', 'success')
    except Exception as e:
        flash(f'خطأ في حذف المكون: {str(e)}', 'error')
    return redirect(url_for('manage_ingredients'))

@app.route('/suggest_mix_for_cow', methods=['POST'])
def suggest_mix_for_cow():
    """API لاقتراح خلطة علف للبقرة"""
    try:
        data = request.get_json()
        requirements = data.get('requirements')
        
        if not requirements:
            return jsonify({'error': 'لم يتم تمرير البيانات المطلوبة'}), 400
        
        # الحصول على جميع المكونات المتاحة
        ingredients = Ingredient.query.all()
        
        if not ingredients:
            return jsonify({'error': 'لا توجد مكونات متاحة في قاعدة البيانات'}), 400
        
        # خوارزمية اقتراح الخلطة
        suggested_mix = generate_optimal_mix(requirements, ingredients)
        
        return jsonify({
            'success': True,
            'suggested_mix': suggested_mix
        })
        
    except Exception as e:
        return jsonify({'error': f'خطأ في اقتراح الخلطة: {str(e)}'}), 500

def generate_optimal_mix(requirements, ingredients):
    """توليد خلطة مثلى بناءً على الاحتياجات والمكونات المتاحة"""
    
    # احتياجات أساسية
    total_dm = requirements['total_dm']  # المادة الجافة المطلوبة
    total_protein = requirements['total_protein']  # البروتين المطلوب (غرام)
    total_energy = requirements['total_energy']  # الطاقة المطلوبة (Mcal)
    
    # تحويل قائمة المكونات إلى قاموس لسهولة الوصول
    ingredients_data = []
    for ing in ingredients:
        ingredients_data.append({
            'id': ing.id,
            'name': ing.name,
            'protein': ing.protein,  # نسبة مئوية
            'energy': ing.energy,    # kcal/kg
            'fiber': ing.fiber,      # نسبة مئوية
            'price_per_kg': ing.price_per_kg
        })
    
    # خوارزمية بسيطة لتكوين الخلطة
    mix_components = []
    total_weight = 0
    total_cost = 0
    provided_protein = 0
    provided_energy = 0
    
    # أولوية المكونات (الأساسية أولاً)
    essential_ingredients = ['شعير', 'ذرة', 'كسب فول الصويا', 'نخالة القمح']
    
    # البحث عن المكونات الأساسية
    base_ingredients = []
    supplement_ingredients = []
    
    for ing in ingredients_data:
        if any(essential in ing['name'] for essential in essential_ingredients):
            base_ingredients.append(ing)
        else:
            supplement_ingredients.append(ing)
    
    # ترتيب المكونات حسب الكفاءة (البروتين/السعر أو الطاقة/السعر)
    base_ingredients.sort(key=lambda x: (x['protein'] + x['energy']/1000) / x['price_per_kg'], reverse=True)
    supplement_ingredients.sort(key=lambda x: x['protein'] / x['price_per_kg'], reverse=True)
    
    # تكوين الخلطة الأساسية
    remaining_dm = total_dm
    remaining_protein = total_protein
    remaining_energy = total_energy * 1000  # تحويل إلى kcal
    
    # إضافة المكونات الأساسية (60-70% من الخلطة)
    for ing in base_ingredients[:3]:  # أفضل 3 مكونات أساسية
        if remaining_dm <= 0:
            break
            
        # حساب الكمية المناسبة
        proportion = min(0.3, remaining_dm / total_dm)  # حد أقصى 30% لكل مكون
        quantity = total_dm * proportion
        
        if quantity > 0.5:  # حد أدنى 0.5 كغ
            mix_components.append({
                'ingredient': ing,
                'quantity': round(quantity, 2),
                'percentage': round((quantity / total_dm) * 100, 1),
                'reason': 'مصدر أساسي للطاقة والبروتين'
            })
            
            total_weight += quantity
            total_cost += quantity * ing['price_per_kg']
            provided_protein += quantity * (ing['protein'] / 100) * 1000  # غرام
            provided_energy += quantity * ing['energy']  # kcal
            
            remaining_dm -= quantity
            remaining_protein = max(0, remaining_protein - quantity * (ing['protein'] / 100) * 1000)
            remaining_energy = max(0, remaining_energy - quantity * ing['energy'])
    
    # إضافة مكملات البروتين إذا كان هناك نقص
    if remaining_protein > 0 and remaining_dm > 0:
        protein_sources = [ing for ing in supplement_ingredients if ing['protein'] > 15]
        for ing in protein_sources[:2]:
            needed_quantity = min(remaining_dm * 0.15, remaining_protein / (ing['protein'] / 100) / 1000)
            needed_quantity = min(needed_quantity, remaining_dm)
            
            if needed_quantity > 0.2:
                mix_components.append({
                    'ingredient': ing,
                    'quantity': round(needed_quantity, 2),
                    'percentage': round((needed_quantity / total_dm) * 100, 1),
                    'reason': 'مكمل البروتين'
                })
                
                total_weight += needed_quantity
                total_cost += needed_quantity * ing['price_per_kg']
                provided_protein += needed_quantity * (ing['protein'] / 100) * 1000
                provided_energy += needed_quantity * ing['energy']
                
                remaining_dm -= needed_quantity
                remaining_protein = max(0, remaining_protein - needed_quantity * (ing['protein'] / 100) * 1000)
                remaining_energy = max(0, remaining_energy - needed_quantity * ing['energy'])
    
    # ملء الباقي بمكونات إضافية
    if remaining_dm > 0:
        for ing in supplement_ingredients:
            if remaining_dm <= 0.5:
                break
                
            quantity = min(remaining_dm, total_dm * 0.1)  # حد أقصى 10%
            
            if quantity > 0.2:
                mix_components.append({
                    'ingredient': ing,
                    'quantity': round(quantity, 2),
                    'percentage': round((quantity / total_dm) * 100, 1),
                    'reason': 'مكمل غذائي'
                })
                
                total_weight += quantity
                total_cost += quantity * ing['price_per_kg']
                provided_protein += quantity * (ing['protein'] / 100) * 1000
                provided_energy += quantity * ing['energy']
                
                remaining_dm -= quantity
    
    # حساب مؤشرات الجودة
    protein_match = round((provided_protein / total_protein) * 100, 1) if total_protein > 0 else 100
    energy_match = round((provided_energy / (total_energy * 1000)) * 100, 1) if total_energy > 0 else 100
    
    # تحديد مستوى الكفاءة
    if protein_match >= 95 and energy_match >= 95:
        cost_efficiency = "ممتاز"
    elif protein_match >= 85 and energy_match >= 85:
        cost_efficiency = "جيد"
    else:
        cost_efficiency = "مقبول"
    
    # إنشاء التوصيات
    recommendations = []
    
    if protein_match >= 100:
        recommendations.append({
            'type': 'good',
            'message': 'مستوى البروتين ممتاز',
            'suggestion': 'الخلطة تلبي احتياجات البروتين بشكل مثالي'
        })
    elif protein_match < 90:
        recommendations.append({
            'type': 'protein_low',
            'message': 'مستوى البروتين منخفض',
            'suggestion': 'يُنصح بإضافة مصادر بروتين إضافية مثل كسب فول الصويا'
        })
    
    if energy_match >= 100:
        recommendations.append({
            'type': 'good',
            'message': 'مستوى الطاقة ممتاز',
            'suggestion': 'الخلطة تلبي احتياجات الطاقة بشكل مثالي'
        })
    elif energy_match < 90:
        recommendations.append({
            'type': 'energy_low',
            'message': 'مستوى الطاقة منخفض',
            'suggestion': 'يُنصح بإضافة مصادر طاقة إضافية مثل الذرة أو الشعير'
        })
    
    if total_cost / total_dm < 0.5:
        recommendations.append({
            'type': 'good',
            'message': 'التكلفة اقتصادية',
            'suggestion': 'الخلطة ذات تكلفة منخفضة ومناسبة'
        })
    
    # النتيجة النهائية
    return {
        'mix_components': mix_components,
        'totals': {
            'weight': round(total_weight, 2),
            'cost': round(total_cost, 2),
            'cost_per_kg': round(total_cost / max(total_weight, 1), 3)
        },
        'quality_indicators': {
            'protein_match': protein_match,
            'energy_match': energy_match,
            'cost_efficiency': cost_efficiency
        },
        'recommendations': recommendations
    }


if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)