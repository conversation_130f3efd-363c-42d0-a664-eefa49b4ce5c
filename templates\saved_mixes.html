{% extends "base.html" %}

{% block title %}الخلطات المحفوظة - برنامج تكوين الأعلاف المركزة{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-archive me-2"></i>
                    الخلطات المحفوظة
                </h5>
            </div>
            <div class="card-body">
                {% if mixes %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>اسم الخلطة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الوزن الإجمالي (كغ)</th>
                                    <th>التكلفة الإجمالية (د.أ)</th>
                                    <th>متوسط البروتين (%)</th>
                                    <th>متوسط الطاقة (kcal/kg)</th>
                                    <th>متوسط الألياف (%)</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for mix in mixes %}
                                <tr>
                                    <td><strong>{{ mix.name }}</strong></td>
                                    <td>{{ mix.date_created[:16] if mix.date_created else 'غير محدد' }}</td>
                                    <td>{{ "%.2f"|format(mix.total_weight) }}</td>
                                    <td>{{ "%.3f"|format(mix.total_cost) }}</td>
                                    <td>{{ "%.2f"|format(mix.avg_protein) }}</td>
                                    <td>{{ "%.2f"|format(mix.avg_energy) }}</td>
                                    <td>{{ "%.2f"|format(mix.avg_fiber) }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('view_mix', mix_id=mix.id) }}" 
                                               class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i>
                                                عرض
                                            </a>
                                            <button type="button" 
                                                    class="btn btn-sm btn-danger"
                                                    onclick="deleteMix({{ mix.id }}, '{{ mix.name }}')">
                                                <i class="fas fa-trash"></i>
                                                حذف
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد خلطات محفوظة</h5>
                        <p class="text-muted">يمكنك إنشاء خلطة جديدة من الصفحة الرئيسية</p>
                        <a href="{{ url_for('index') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إنشاء خلطة جديدة
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- نافذة تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الخلطة: <strong id="mixNameToDelete"></strong>؟</p>
                <p class="text-danger">لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف الخلطة</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let deleteModal = null;

document.addEventListener('DOMContentLoaded', function() {
    deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
});

function deleteMix(mixId, mixName) {
    document.getElementById('mixNameToDelete').textContent = mixName;
    document.getElementById('deleteForm').action = `/delete_mix/${mixId}`;
    deleteModal.show();
}
</script>
{% endblock %}