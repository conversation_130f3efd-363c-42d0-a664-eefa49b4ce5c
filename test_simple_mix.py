#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار بسيط لخاصية اقتراح الخلطة
"""

import requests
import json

def test_suggest_mix():
    """اختبار اقتراح الخلطة"""
    
    print("🧪 اختبار اقتراح الخلطة للبقرة")
    print("=" * 50)
    
    # البيانات التجريبية
    test_requirements = {
        'total_dm': 18.5,      # إجمالي المادة الجافة كغ
        'total_protein': 2850,  # إجمالي البروتين غرام
        'total_energy': 32.4,   # إجمالي الطاقة Mcal
        'cow_weight': 550,
        'milk_production': 25
    }
    
    try:
        print("📤 إرسال طلب اقتراح الخلطة...")
        print(f"   المتطلبات: {test_requirements}")
        
        # إرسال الطلب
        response = requests.post(
            'http://localhost:5000/suggest_mix_for_cow',
            json={'requirements': test_requirements},
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"📨 كود الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ تم استلام اقتراح الخلطة بنجاح!")
            
            if 'suggested_mix' in data:
                mix = data['suggested_mix']
                
                print("\n🌾 مكونات الخلطة المقترحة:")
                if 'mix_components' in mix:
                    for component in mix['mix_components']:
                        ing = component['ingredient']
                        print(f"  - {ing['name']}: {component['quantity']} كغ ({component['percentage']}%)")
                        print(f"    السبب: {component['reason']}")
                
                print("\n💰 معلومات التكلفة:")
                if 'totals' in mix:
                    totals = mix['totals']
                    print(f"  - الوزن الإجمالي: {totals['weight']} كغ")
                    print(f"  - التكلفة الإجمالية: {totals['cost']} د.أ")
                    print(f"  - التكلفة/كغ: {totals['cost_per_kg']} د.أ")
                
                print("\n📊 مؤشرات الجودة:")
                if 'quality_indicators' in mix:
                    qi = mix['quality_indicators']
                    print(f"  - مطابقة البروتين: {qi['protein_match']}%")
                    print(f"  - مطابقة الطاقة: {qi['energy_match']}%")
                    print(f"  - كفاءة التكلفة: {qi['cost_efficiency']}")
                
                print("\n💡 التوصيات:")
                if 'recommendations' in mix:
                    for rec in mix['recommendations']:
                        print(f"  - {rec['message']}")
                        print(f"    {rec['suggestion']}")
                
            print("\n🎉 اختبار اقتراح الخلطة نجح!")
                
        else:
            print(f"❌ خطأ في الاستجابة: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   رسالة الخطأ: {error_data.get('error', 'خطأ غير محدد')}")
            except:
                print(f"   نص الاستجابة: {response.text}")
    
    except requests.exceptions.ConnectionError:
        print("❌ خطأ في الاتصال - تأكد من تشغيل البرنامج على http://localhost:5000")
    except requests.exceptions.Timeout:
        print("❌ انتهت مهلة الاتصال")
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")

def test_server_status():
    """اختبار حالة الخادم"""
    try:
        response = requests.get('http://localhost:5000/', timeout=5)
        if response.status_code == 200:
            print("✅ الخادم يعمل بشكل طبيعي")
            return True
        else:
            print(f"⚠️ الخادم يستجيب بكود: {response.status_code}")
            return False
    except:
        print("❌ الخادم لا يستجيب")
        return False

if __name__ == '__main__':
    print("🔧 اختبار حالة الخادم...")
    if test_server_status():
        print()
        test_suggest_mix()
    else:
        print("\n💡 لتشغيل الخادم:")
        print("   python run.py")
    
    print("=" * 50)
    print("🏁 انتهى الاختبار")