#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج تكوين الأعلاف المركزة
تشغيل البرنامج
"""

from app import app, db
import os

if __name__ == '__main__':
    print("=" * 60)
    print("🌾 برنامج تكوين الأعلاف المركزة 🌾")
    print("=" * 60)
    print("📊 إنشاء قاعدة البيانات...")
    
    # إنشاء قاعدة البيانات إذا لم تكن موجودة
    with app.app_context():
        db.create_all()
    print("✅ تم إنشاء قاعدة البيانات بنجاح")
    
    print("\n🌐 تشغيل الخادم...")
    print("📋 يمكنك الوصول للبرنامج عبر:")
    print("   🔗 http://localhost:5000")
    print("   🔗 http://127.0.0.1:5000")
    print("\n📖 الصفحات المتاحة:")
    print("   🏠 الصفحة الرئيسية: /")
    print("   ⚙️  إدارة المكونات: /manage_ingredients")
    print("   🐄 احتياجات البقرة: /cow_requirements")
    print("   🦌 احتياجات جميع الحيوانات: /animal_requirements")
    print("   📊 تاريخ حسابات الأبقار: /cow_history")
    print("   ⚖️  مقارنة الأبقار: /compare_cows")
    print("   💾 الخلطات المحفوظة: /saved_mixes")
    print("\n🛑 لإيقاف البرنامج اضغط Ctrl+C")
    print("=" * 60)
    
    try:
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            use_reloader=True
        )
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف البرنامج بأمان")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل البرنامج: {e}")