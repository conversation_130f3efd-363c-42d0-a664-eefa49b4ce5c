#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_mix_suggestion():
    """اختبار وظيفة اقتراح الخلطة"""
    
    base_url = "http://localhost:5000"
    
    print("🧪 اختبار وظيفة اقتراح الخلطة...")
    
    # بيانات احتياجات البقرة للاختبار
    requirements = {
        "total_dm": 24.26,
        "total_protein": 1882,
        "total_energy": 28.49,
        "protein_percentage": 7.8,
        "daily_cost": 9.645
    }
    
    print(f"📊 الاحتياجات المرسلة: {requirements}")
    
    try:
        response = requests.post(f"{base_url}/suggest_mix_for_cow", 
                               json={"requirements": requirements})
        
        print(f"كود الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                print("✅ نجح اقتراح الخلطة!")
                
                suggested_mix = data['suggested_mix']
                
                print("\n📋 تفاصيل الخلطة المقترحة:")
                print("=" * 50)
                
                # عرض المكونات
                print("🌾 مكونات الخلطة:")
                for i, component in enumerate(suggested_mix['mix_components'], 1):
                    ingredient = component['ingredient']
                    print(f"{i}. {ingredient['name']}")
                    print(f"   الكمية: {component['quantity']} كغ ({component['percentage']}%)")
                    print(f"   السبب: {component['reason']}")
                    print(f"   السعر: {ingredient['price_per_kg']} ريال/كغ")
                    print()
                
                # عرض الإجماليات
                totals = suggested_mix['totals']
                print("💰 الإجماليات:")
                print(f"   إجمالي الوزن: {totals['weight']} كغ")
                print(f"   إجمالي التكلفة: {totals['cost']} ريال")
                print(f"   تكلفة الكيلو: {totals['cost_per_kg']} ريال/كغ")
                print()
                
                # عرض مؤشرات الجودة
                quality = suggested_mix['quality_indicators']
                print("📊 مؤشرات الجودة:")
                print(f"   دقة البروتين: {quality['protein_match']}%")
                print(f"   دقة الطاقة: {quality['energy_match']}%")
                print(f"   كفاءة التكلفة: {quality['cost_efficiency']}")
                print()
                
                # عرض التوصيات
                print("💡 التوصيات:")
                for i, rec in enumerate(suggested_mix['recommendations'], 1):
                    print(f"{i}. {rec['message']}")
                    print(f"   الاقتراح: {rec['suggestion']}")
                    print()
                
            else:
                print(f"❌ فشل في اقتراح الخلطة: {data.get('error', 'خطأ غير محدد')}")
        else:
            print(f"❌ فشل في الطلب: {response.text}")
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")

if __name__ == "__main__":
    test_mix_suggestion()
