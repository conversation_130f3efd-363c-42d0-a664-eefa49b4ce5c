#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف التشغيل الرئيسي لبرنامج تكوين الأعلاف المركزة
Feed Formulation System Main Entry Point
"""

import os
import sys
import webbrowser
import threading
import time
from app_simple import app, init_db

def resource_path(relative_path):
    """الحصول على المسار الصحيح للملفات المضمنة في exe"""
    try:
        # PyInstaller يضع الملفات في مجلد مؤقت
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

def setup_database():
    """إعداد قاعدة البيانات"""
    print("🔧 إعداد قاعدة البيانات...")
    try:
        init_db()
        print("✅ تم إعداد قاعدة البيانات بنجاح")
        return True
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def open_browser():
    """فتح المتصفح تلقائياً بعد 2 ثانية"""
    time.sleep(2)
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 تم فتح المتصفح على http://localhost:5000")
    except Exception as e:
        print(f"⚠️ لا يمكن فتح المتصفح تلقائياً: {e}")
        print("يرجى فتح المتصفح وزيارة: http://localhost:5000")

def main():
    """الدالة الرئيسية"""
    print("🌾 برنامج تكوين الأعلاف المركزة")
    print("=" * 50)
    print("🚀 بدء تشغيل البرنامج...")
    
    # إعداد قاعدة البيانات
    if not setup_database():
        input("اضغط Enter للخروج...")
        return
    
    # تشغيل خيط منفصل لفتح المتصفح
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    print("🌐 الخادم يعمل على: http://localhost:5000")
    print("📱 سيتم فتح المتصفح تلقائياً...")
    print("⚠️ لإيقاف البرنامج: اضغط Ctrl+C")
    print("=" * 50)
    
    try:
        # تشغيل الخادم
        app.run(
            host='127.0.0.1',
            port=5000,
            debug=False,
            use_reloader=False,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()