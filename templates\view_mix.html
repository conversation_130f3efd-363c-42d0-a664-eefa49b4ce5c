{% extends "base.html" %}

{% block title %}{{ mix.name }} - برنامج تكوين الأعلاف المركزة{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- عنوان الخلطة -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-eye me-2"></i>
                        {{ mix.name }}
                    </h4>
                    <small>تاريخ الإنشاء: {{ mix['date_created'].strftime('%Y-%m-%d %H:%M') }}</small>
                </div>
            </div>
        </div>

        <!-- ملخص الخلطة -->
        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-pie me-2"></i>
                            ملخص الخلطة
                        </h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong><i class="fas fa-weight me-2 text-primary"></i>إجمالي الوزن:</strong></td>
                                <td>{{ "%.2f"|format(mix.total_weight) }} كغ</td>
                            </tr>
                            <tr>
                                <td><strong><i class="fas fa-money-bill-wave me-2 text-success"></i>إجمالي التكلفة:</strong></td>
                                <td>{{ "%.3f"|format(mix.total_cost) }} د.أ</td>
                            </tr>
                            <tr>
                                <td><strong><i class="fas fa-calculator me-2 text-info"></i>التكلفة/كغ:</strong></td>
                                <td>{{ "%.3f"|format(mix.total_cost / mix.total_weight) }} د.أ</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">
                            <i class="fas fa-flask me-2"></i>
                            التحليل الغذائي
                        </h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong><i class="fas fa-dna me-2 text-primary"></i>متوسط البروتين:</strong></td>
                                <td>{{ "%.2f"|format(mix.avg_protein) }}%</td>
                            </tr>
                            <tr>
                                <td><strong><i class="fas fa-bolt me-2 text-warning"></i>متوسط الطاقة:</strong></td>
                                <td>{{ "%.2f"|format(mix.avg_energy) }} kcal/kg</td>
                            </tr>
                            <tr>
                                <td><strong><i class="fas fa-leaf me-2 text-success"></i>متوسط الألياف:</strong></td>
                                <td>{{ "%.2f"|format(mix.avg_fiber) }}%</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- تفاصيل المكونات -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-list-ul me-2"></i>
                    تفاصيل المكونات
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>المكون</th>
                                <th>الكمية (كغ)</th>
                                <th>النسبة من الخلطة (%)</th>
                                <th>البروتين (%)</th>
                                <th>الطاقة (kcal/kg)</th>
                                <th>الألياف (%)</th>
                                <th>السعر/كغ (د.أ)</th>
                                <th>التكلفة الإجمالية (د.أ)</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for detail in mix['details'] %}
                            <tr>
                                <td>
                                    <strong>{{ detail.name }}</strong>
                                </td>
                                <td>{{ "%.2f"|format(detail.quantity_kg) }}</td>
                                <td>{{ "%.1f"|format((detail.quantity_kg / mix.total_weight) * 100) }}%</td>
                                <td>{{ detail.protein }}</td>
                                <td>{{ detail.energy }}</td>
                                <td>{{ detail.fiber }}</td>
                                <td>{{ "%.3f"|format(detail.price_per_kg) }}</td>
                                <td>{{ "%.3f"|format(detail.quantity_kg * detail.price_per_kg) }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-secondary">
                            <tr>
                                <td><strong>المجموع</strong></td>
                                <td><strong>{{ "%.2f"|format(mix.total_weight) }}</strong></td>
                                <td><strong>100%</strong></td>
                                <td><strong>{{ "%.2f"|format(mix.avg_protein) }}</strong></td>
                                <td><strong>{{ "%.2f"|format(mix.avg_energy) }}</strong></td>
                                <td><strong>{{ "%.2f"|format(mix.avg_fiber) }}</strong></td>
                                <td>-</td>
                                <td><strong>{{ "%.3f"|format(mix.total_cost) }}</strong></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>

        <!-- أزرار التحكم -->
        <div class="card mt-4">
            <div class="card-body text-center">
                <div class="btn-group" role="group">
                    <a href="{{ url_for('saved_mixes') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للخلطات المحفوظة
                    </a>
                    <button type="button" class="btn btn-primary" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>
                        طباعة التقرير
                    </button>
                    <button type="button" class="btn btn-danger" onclick="deleteMix({{ mix.id }}, '{{ mix.name }}')">
                        <i class="fas fa-trash me-2"></i>
                        حذف الخلطة
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الخلطة: <strong id="mixNameToDelete"></strong>؟</p>
                <p class="text-danger">لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف الخلطة</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
@media print {
    .btn-group, .card-header, .navbar, footer {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .table {
        font-size: 12px;
    }
    
    body {
        font-size: 12px;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let deleteModal = null;

document.addEventListener('DOMContentLoaded', function() {
    deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
});

function deleteMix(mixId, mixName) {
    document.getElementById('mixNameToDelete').textContent = mixName;
    document.getElementById('deleteForm').action = `/delete_mix/${mixId}`;
    deleteModal.show();
    
    // بعد الحذف، الانتقال إلى صفحة الخلطات المحفوظة
    document.getElementById('deleteForm').addEventListener('submit', function() {
        setTimeout(() => {
            window.location.href = "{{ url_for('saved_mixes') }}";
        }, 100);
    });
}
</script>
{% endblock %}