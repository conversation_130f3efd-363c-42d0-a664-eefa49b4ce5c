#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء أيقونة للبرنامج
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon():
    """إنشاء أيقونة للبرنامج"""
    try:
        # إنشاء صورة 256x256
        size = 256
        img = Image.new('RGB', (size, size), color='#2E7D32')  # أخضر زراعي
        draw = ImageDraw.Draw(img)
        
        # رسم دائرة خارجية
        margin = 20
        draw.ellipse([margin, margin, size-margin, size-margin], 
                    fill='#4CAF50', outline='#1B5E20', width=8)
        
        # رسم رمز الزراعة 🌾
        center = size // 2
        
        # رسم سيقان القمح
        for i in range(3):
            x = center - 30 + (i * 30)
            # الساق
            draw.rectangle([x-3, center-20, x+3, center+60], fill='#8BC34A')
            
            # الحبوب
            for j in range(8):
                y = center - 15 + (j * 8)
                draw.ellipse([x-8, y-3, x+8, y+3], fill='#FFC107')
        
        # رسم النص
        try:
            # محاولة استخدام خط عربي
            font_large = ImageFont.truetype("arial.ttf", 40)
            font_small = ImageFont.truetype("arial.ttf", 20)
        except:
            font_large = ImageFont.load_default()
            font_small = ImageFont.load_default()
        
        # النص العلوي
        text1 = "Feed"
        bbox1 = draw.textbbox((0, 0), text1, font=font_large)
        text_width1 = bbox1[2] - bbox1[0]
        draw.text(((size - text_width1) // 2, 40), text1, 
                 fill='white', font=font_large)
        
        # النص السفلي
        text2 = "Mixer"
        bbox2 = draw.textbbox((0, 0), text2, font=font_large)
        text_width2 = bbox2[2] - bbox2[0]
        draw.text(((size - text_width2) // 2, size - 80), text2, 
                 fill='white', font=font_large)
        
        # حفظ الأيقونة
        icon_path = 'app_icon.ico'
        
        # إنشاء أحجام مختلفة للأيقونة
        sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
        icons = []
        
        for size_tuple in sizes:
            resized = img.resize(size_tuple, Image.Resampling.LANCZOS)
            icons.append(resized)
        
        # حفظ كـ ICO
        icons[0].save(icon_path, format='ICO', sizes=[s for s in sizes])
        
        print(f"✅ تم إنشاء الأيقونة: {icon_path}")
        return icon_path
        
    except Exception as e:
        print(f"⚠️ لا يمكن إنشاء الأيقونة: {e}")
        return None

if __name__ == "__main__":
    create_icon()