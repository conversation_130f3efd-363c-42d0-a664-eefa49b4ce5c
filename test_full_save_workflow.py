#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_complete_workflow():
    """اختبار سير العمل الكامل من حساب الاحتياجات إلى حفظ الخلطة"""
    
    base_url = "http://localhost:5000"
    
    print("🚀 اختبار سير العمل الكامل لحفظ الخلطة...")
    print("=" * 60)
    
    # الخطوة 1: حساب احتياجات البقرة
    print("1️⃣ حساب احتياجات البقرة...")
    
    cow_data = {
        "animal_type": "cow",
        "cow_weight": 500,
        "cow_milk": 25,
        "cow_stage": "mid",
        "activity_level": "moderate"
    }
    
    try:
        response = requests.post(f"{base_url}/calculate_animal_requirements", json=cow_data)
        
        if response.status_code == 200:
            requirements_data = response.json()
            print("   ✅ تم حساب الاحتياجات بنجاح")
            
            requirements = requirements_data['requirements']
            print(f"   📊 المادة الجافة: {requirements['total_dm']} كغ/يوم")
            print(f"   🥩 البروتين: {requirements['total_protein']} غرام/يوم")
            print(f"   ⚡ الطاقة: {requirements['total_energy']} Mcal/يوم")
            print(f"   💰 التكلفة: {requirements['daily_cost']} د.أ/يوم")
            
        else:
            print(f"   ❌ فشل في حساب الاحتياجات: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في حساب الاحتياجات: {e}")
        return False
    
    # الخطوة 2: اقتراح الخلطة
    print("\n2️⃣ اقتراح الخلطة...")
    
    try:
        response = requests.post(f"{base_url}/suggest_mix_for_cow", 
                               json={"requirements": requirements})
        
        if response.status_code == 200:
            mix_data = response.json()
            
            if mix_data.get('success'):
                print("   ✅ تم اقتراح الخلطة بنجاح")
                
                suggested_mix = mix_data['suggested_mix']
                
                print(f"   📋 عدد المكونات: {len(suggested_mix['mix_components'])}")
                print(f"   💰 إجمالي التكلفة: {suggested_mix['totals']['cost']} د.أ")
                print(f"   📊 دقة البروتين: {suggested_mix['quality_indicators']['protein_match']}")
                print(f"   📊 دقة الطاقة: {suggested_mix['quality_indicators']['energy_match']}")
                
                # عرض المكونات
                print("\n   🌾 مكونات الخلطة:")
                for i, component in enumerate(suggested_mix['mix_components'], 1):
                    ingredient = component['ingredient']
                    print(f"      {i}. {ingredient['name']}: {component['quantity']} كغ ({component['percentage']}%)")
                
            else:
                print(f"   ❌ فشل في اقتراح الخلطة: {mix_data.get('error', 'خطأ غير محدد')}")
                return False
        else:
            print(f"   ❌ فشل في طلب الخلطة: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في اقتراح الخلطة: {e}")
        return False

    # الخطوة 3: محاكاة حفظ الخلطة
    print("\n3️⃣ اختبار حفظ الخلطة...")
    
    # تحويل مكونات الخلطة المقترحة لتنسيق الحفظ
    ingredients_for_save = []
    for component in suggested_mix['mix_components']:
        ingredient = component['ingredient']
        ingredients_for_save.append({
            "id": ingredient.get('id', 1),  # استخدام ID أو قيمة افتراضية
            "quantity": component['quantity']
        })
    
    save_data = {
        "name": "خلطة مقترحة من اختبار البقرة",
        "ingredients": ingredients_for_save,
        "calculations": {
            "total_weight": suggested_mix['totals']['weight'],
            "total_cost": suggested_mix['totals']['cost'],
            "avg_protein": 15.0,  # قيمة تقديرية
            "avg_energy": 2800.0,  # قيمة تقديرية
            "avg_fiber": 12.0  # قيمة تقديرية للألياف
        }
    }
    
    try:
        response = requests.post(f"{base_url}/save_mix", json=save_data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("   ✅ تم حفظ الخلطة بنجاح")
                print(f"   📝 الرسالة: {result.get('message', 'تم الحفظ')}")
                return True
            else:
                print(f"   ❌ فشل في حفظ الخلطة: {result.get('error', 'خطأ غير محدد')}")
                return False
        else:
            print(f"   ❌ فشل في طلب الحفظ: {response.status_code}")
            print(f"   📝 الرد: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في حفظ الخلطة: {e}")
        return False

def test_saved_mixes_page():
    """اختبار صفحة الخلطات المحفوظة"""
    
    base_url = "http://localhost:5000"
    
    print("\n4️⃣ اختبار صفحة الخلطات المحفوظة...")
    
    try:
        response = requests.get(f"{base_url}/saved_mixes")
        
        if response.status_code == 200:
            print("   ✅ صفحة الخلطات المحفوظة تعمل بشكل صحيح")
            
            # التحقق من وجود الخلطة المحفوظة في المحتوى
            content = response.text
            if "خلطة مقترحة من اختبار البقرة" in content:
                print("   ✅ تم العثور على الخلطة المحفوظة في الصفحة")
            else:
                print("   ⚠️ لم يتم العثور على الخلطة المحفوظة في الصفحة")
            
            return True
        else:
            print(f"   ❌ فشل في الوصول لصفحة الخلطات: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في الوصول لصفحة الخلطات: {e}")
        return False

def test_main_page_functionality():
    """اختبار وظيفة الصفحة الرئيسية"""
    
    base_url = "http://localhost:5000"
    
    print("\n5️⃣ اختبار الصفحة الرئيسية...")
    
    try:
        response = requests.get(f"{base_url}/")
        
        if response.status_code == 200:
            print("   ✅ الصفحة الرئيسية تعمل بشكل صحيح")
            
            content = response.text
            
            # التحقق من وجود العناصر المهمة
            checks = [
                ("checkForSuggestedMix", "دالة فحص الخلطة المقترحة"),
                ("loadSuggestedMix", "دالة تحميل الخلطة المقترحة"),
                ("localStorage", "استخدام Local Storage"),
                ("mixtureIngredients", "متغير مكونات الخلطة")
            ]
            
            for check, description in checks:
                if check in content:
                    print(f"   ✅ {description} موجود")
                else:
                    print(f"   ❌ {description} مفقود")
            
            return True
        else:
            print(f"   ❌ فشل في الوصول للصفحة الرئيسية: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في الوصول للصفحة الرئيسية: {e}")
        return False

if __name__ == "__main__":
    print("🎯 اختبار شامل لوظيفة حفظ الخلطة المقترحة")
    print("🔄 السير: حساب احتياجات → اقتراح خلطة → حفظ خلطة → عرض محفوظة")
    print()
    
    # تشغيل الاختبارات
    workflow_ok = test_complete_workflow()
    saved_page_ok = test_saved_mixes_page()
    main_page_ok = test_main_page_functionality()
    
    print("\n" + "=" * 60)
    print("📊 ملخص النتائج النهائي:")
    print(f"   سير العمل الكامل: {'✅ يعمل' if workflow_ok else '❌ لا يعمل'}")
    print(f"   صفحة الخلطات المحفوظة: {'✅ تعمل' if saved_page_ok else '❌ لا تعمل'}")
    print(f"   الصفحة الرئيسية: {'✅ تعمل' if main_page_ok else '❌ لا تعمل'}")
    
    if workflow_ok and saved_page_ok and main_page_ok:
        print("\n🎉 جميع الوظائف تعمل بشكل مثالي!")
        print("\n💡 الآن يمكنك:")
        print("   1. فتح صفحة احتياجات البقرة: http://localhost:5000/cow_requirements")
        print("   2. حساب احتياجات البقرة")
        print("   3. اقتراح خلطة مناسبة")
        print("   4. الضغط على 'قبول الخلطة وحفظها'")
        print("   5. سيتم نقلك للصفحة الرئيسية مع الخلطة محملة")
        print("   6. تعديل الخلطة حسب الحاجة")
        print("   7. حفظ الخلطة نهائياً")
        print("   8. مراجعة الخلطات المحفوظة: http://localhost:5000/saved_mixes")
    else:
        print("\n⚠️ بعض الوظائف تحتاج إصلاح")
        
    print(f"\n🌐 الروابط المهمة:")
    print(f"   📊 احتياجات البقرة: http://localhost:5000/cow_requirements")
    print(f"   🏠 الصفحة الرئيسية: http://localhost:5000/")
    print(f"   📚 الخلطات المحفوظة: http://localhost:5000/saved_mixes")
