<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط للزر</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="card">
            <div class="card-header">
                <h5>🧪 اختبار بسيط لزر حساب الاحتياجات</h5>
            </div>
            <div class="card-body">
                <!-- نموذج مبسط -->
                <div class="row">
                    <div class="col-md-3">
                        <label for="cowWeight" class="form-label">وزن البقرة (كغ)</label>
                        <input type="number" class="form-control" id="cowWeight" value="500" min="300" max="800">
                    </div>
                    <div class="col-md-3">
                        <label for="milkProduction" class="form-label">إنتاج الحليب (لتر/يوم)</label>
                        <input type="number" class="form-control" id="milkProduction" value="25" min="0" max="80">
                    </div>
                    <div class="col-md-3">
                        <label for="lactationStage" class="form-label">مرحلة الإنتاج</label>
                        <select class="form-select" id="lactationStage">
                            <option value="early">مبكرة</option>
                            <option value="mid" selected>متوسطة</option>
                            <option value="late">متأخرة</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="activityLevel" class="form-label">مستوى النشاط</label>
                        <select class="form-select" id="activityLevel">
                            <option value="low">منخفض</option>
                            <option value="moderate" selected>متوسط</option>
                            <option value="high">مرتفع</option>
                        </select>
                    </div>
                </div>
                
                <div class="mt-3">
                    <button type="button" class="btn btn-success btn-lg" id="calculateBtn">
                        <i class="fas fa-calculator me-2"></i>
                        حساب الاحتياجات
                    </button>
                    
                    <button type="button" class="btn btn-info btn-lg ms-2" id="testBtn">
                        <i class="fas fa-flask me-2"></i>
                        اختبار بسيط
                    </button>
                </div>
                
                <!-- منطقة النتائج -->
                <div class="mt-4">
                    <div class="card">
                        <div class="card-header">
                            <h6>📊 النتائج</h6>
                        </div>
                        <div class="card-body">
                            <div id="results">
                                <p class="text-muted">اضغط على الزر لرؤية النتائج...</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- سجل الأحداث -->
                <div class="mt-3">
                    <div class="card">
                        <div class="card-header">
                            <h6>📝 سجل الأحداث</h6>
                        </div>
                        <div class="card-body">
                            <div id="log" style="max-height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;">
                                <p class="text-muted">سيظهر هنا سجل الأحداث...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // دالة إضافة رسالة للسجل
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'text-danger' : type === 'success' ? 'text-success' : 'text-info';
            logDiv.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        // دالة اختبار بسيط
        function testSimple() {
            log('🧪 اختبار بسيط بدأ');
            alert('الاختبار البسيط يعمل!');
            log('✅ الاختبار البسيط نجح', 'success');
        }
        
        // دالة حساب الاحتياجات
        function calculateRequirements() {
            log('🐄 بدء حساب احتياجات البقرة...');
            
            const cowWeight = parseFloat(document.getElementById('cowWeight').value);
            const milkProduction = parseFloat(document.getElementById('milkProduction').value);
            const lactationStage = document.getElementById('lactationStage').value;
            const activityLevel = document.getElementById('activityLevel').value;
            
            log(`📊 البيانات: وزن=${cowWeight}, حليب=${milkProduction}, مرحلة=${lactationStage}, نشاط=${activityLevel}`);
            
            if (!cowWeight || cowWeight < 300 || cowWeight > 800) {
                log('❌ وزن البقرة غير صحيح', 'error');
                alert('يرجى إدخال وزن صحيح للبقرة (300-800 كغ)');
                return;
            }
            
            if (milkProduction < 0 || milkProduction > 80) {
                log('❌ إنتاج الحليب غير صحيح', 'error');
                alert('يرجى إدخال إنتاج حليب صحيح (0-80 لتر)');
                return;
            }
            
            // إظهار مؤشر التحميل
            const calculateBtn = document.getElementById('calculateBtn');
            const originalText = calculateBtn.innerHTML;
            calculateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحساب...';
            calculateBtn.disabled = true;
            
            log('🌐 إرسال البيانات إلى الخادم...');
            
            const requestData = {
                animal_type: 'cow',
                cow_weight: cowWeight,
                cow_milk: milkProduction,
                cow_stage: lactationStage,
                activity_level: activityLevel
            };
            
            log(`📤 البيانات المرسلة: ${JSON.stringify(requestData)}`);
            
            fetch('http://localhost:5000/calculate_animal_requirements', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                log(`📡 كود الاستجابة: ${response.status}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                log(`📥 البيانات المستقبلة: ${JSON.stringify(data, null, 2)}`);
                
                if (data.error) {
                    log(`❌ خطأ من الخادم: ${data.error}`, 'error');
                    alert('خطأ: ' + data.error);
                    return;
                }
                
                if (data.requirements) {
                    log('✅ تم استقبال البيانات بنجاح!', 'success');
                    displayResults(data.requirements);
                } else {
                    log('❌ البيانات المرجعة لا تحتوي على requirements', 'error');
                    alert('خطأ في استقبال البيانات من الخادم');
                }
            })
            .catch(error => {
                log(`❌ خطأ في الاتصال: ${error.message}`, 'error');
                console.error('خطأ في الاتصال:', error);
                alert('خطأ في الاتصال بالخادم: ' + error.message);
            })
            .finally(() => {
                // إعادة تعيين زر الحساب
                calculateBtn.innerHTML = originalText;
                calculateBtn.disabled = false;
                log('🔄 تم إعادة تعيين الزر');
            });
        }
        
        // دالة عرض النتائج
        function displayResults(req) {
            log('📊 عرض النتائج...');
            
            const resultsDiv = document.getElementById('results');
            
            const html = `
                <div class="row">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h6>المادة الجافة</h6>
                                <h4>${req.total_dm ? req.total_dm.toFixed(1) : '0.0'} كغ</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h6>البروتين</h6>
                                <h4>${req.total_protein ? (req.total_protein / 1000).toFixed(2) : '0.0'} كغ</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h6>الطاقة</h6>
                                <h4>${req.total_energy ? req.total_energy.toFixed(1) : '0.0'} Mcal</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h6>التكلفة اليومية</h6>
                                <h4>${req.daily_cost ? req.daily_cost.toFixed(2) : '0.0'} ريال</h4>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <h6>تفاصيل إضافية:</h6>
                    <ul>
                        <li>نسبة البروتين: ${req.protein_percentage ? req.protein_percentage.toFixed(1) : '0.0'}%</li>
                        <li>الألياف الدنيا: ${req.min_fiber ? req.min_fiber.toFixed(1) : '0.0'} كغ</li>
                        <li>الألياف العليا: ${req.max_fiber ? req.max_fiber.toFixed(1) : '0.0'} كغ</li>
                    </ul>
                </div>
            `;
            
            resultsDiv.innerHTML = html;
            log('✅ تم عرض النتائج بنجاح!', 'success');
        }
        
        // ربط الأحداث عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 تم تحميل الصفحة');
            
            const calculateBtn = document.getElementById('calculateBtn');
            const testBtn = document.getElementById('testBtn');
            
            if (calculateBtn) {
                calculateBtn.addEventListener('click', calculateRequirements);
                log('✅ تم ربط زر حساب الاحتياجات');
            } else {
                log('❌ لم يتم العثور على زر حساب الاحتياجات', 'error');
            }
            
            if (testBtn) {
                testBtn.addEventListener('click', testSimple);
                log('✅ تم ربط زر الاختبار البسيط');
            } else {
                log('❌ لم يتم العثور على زر الاختبار', 'error');
            }
            
            log('🎯 جاهز للاختبار!', 'success');
        });
        
        // اختبار console
        console.log('🧪 JavaScript تم تحميله بنجاح');
    </script>
</body>
</html>
