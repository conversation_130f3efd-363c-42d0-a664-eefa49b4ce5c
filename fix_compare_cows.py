#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح مقارنة الأبقار
"""

from app_simple import app, get_db, init_db
import sqlite3

def fix_compare_cows():
    """إصلاح وتجهيز مقارنة الأبقار"""
    print("🔧 إصلاح مقارنة الأبقار")
    print("=" * 40)
    
    # التأكد من قاعدة البيانات
    init_db()
    
    conn = get_db()
    
    # فحص جدول cow_calculations
    try:
        cursor = conn.execute('SELECT COUNT(*) FROM cow_calculations')
        count = cursor.fetchone()[0]
        print(f"📊 عدد الأبقار المحفوظة: {count}")
        
        if count == 0:
            print("⚠️ لا توجد بيانات - سنضيف بيانات تجريبية...")
            
            # إضافة بيانات تجريبية لـ 5 أبقار
            test_cows = [
                ('بقرة الصباح', 550, 28, 'mid', 'moderate', 25.4, 1950, 30.2, 10.16),
                ('بقرة النور', 600, 32, 'early', 'high', 29.8, 2180, 35.5, 11.92),
                ('بقرة الخير', 480, 22, 'late', 'low', 21.6, 1680, 26.1, 8.64),
                ('بقرة البركة', 650, 35, 'mid', 'moderate', 32.5, 2450, 38.8, 13.00),
                ('بقرة السعادة', 520, 25, 'early', 'moderate', 24.8, 1875, 29.6, 9.92)
            ]
            
            for cow_data in test_cows:
                conn.execute('''
                    INSERT INTO cow_calculations 
                    (cow_name, weight, milk_production, lactation_stage, activity_level,
                     total_dm, total_protein, total_energy, daily_cost, calculation_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
                ''', cow_data)
            
            conn.commit()
            print(f"✅ تم إضافة {len(test_cows)} أبقار تجريبية")
        
        # عرض البيانات الموجودة
        cursor = conn.execute('''
            SELECT cow_name, weight, milk_production, daily_cost, calculation_date
            FROM cow_calculations 
            ORDER BY calculation_date DESC 
            LIMIT 5
        ''')
        
        rows = cursor.fetchall()
        print("\n📋 الأبقار المتاحة للمقارنة:")
        for i, row in enumerate(rows, 1):
            print(f"  {i}. {row[0]} - {row[1]}كغ - {row[2]}ل/يوم - {row[3]:.2f}د.أ")
    
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
    
    conn.close()
    
    # اختبار صفحة المقارنة
    print("\n🌐 اختبار صفحة المقارنة...")
    
    with app.test_client() as client:
        try:
            response = client.get('/compare_cows')
            if response.status_code == 200:
                print("✅ صفحة المقارنة تعمل بنجاح!")
                print("🔗 يمكنك الوصول إليها على: http://localhost:5000/compare_cows")
            else:
                print(f"❌ خطأ في تحميل الصفحة: {response.status_code}")
                
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
    
    print("\n" + "=" * 40)
    print("🎉 تم إعداد مقارنة الأبقار!")

if __name__ == "__main__":
    fix_compare_cows()