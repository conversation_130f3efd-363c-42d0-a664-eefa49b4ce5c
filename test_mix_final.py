#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نهائي لخاصية اقتراح الخلطة
"""

import requests
import json
import time

def test_cow_mix_suggestion():
    """اختبار شامل لاقتراح الخلطة"""
    
    print("🧪 اختبار اقتراح الخلطة - النسخة النهائية")
    print("=" * 60)
    
    try:
        # اختبار 1: التحقق من تشغيل البرنامج
        print("🔧 اختبار الاتصال...")
        response = requests.get('http://localhost:5000/', timeout=5)
        print(f"✅ البرنامج يعمل على المنفذ 5000 (كود: {response.status_code})")
        
        # اختبار 2: بيانات تجريبية مختلفة
        test_cases = [
            {
                'name': 'بقرة إنتاج عالي',
                'data': {
                    'total_dm': 20.5,
                    'total_protein': 3200,
                    'total_energy': 38.2,
                    'cow_weight': 600,
                    'milk_production': 35
                }
            },
            {
                'name': 'بقرة إنتاج متوسط',
                'data': {
                    'total_dm': 18.5,
                    'total_protein': 2850,
                    'total_energy': 32.4,
                    'cow_weight': 550,
                    'milk_production': 25
                }
            },
            {
                'name': 'بقرة إنتاج منخفض',
                'data': {
                    'total_dm': 15.0,
                    'total_protein': 2200,
                    'total_energy': 26.8,
                    'cow_weight': 450,
                    'milk_production': 15
                }
            }
        ]
        
        success_count = 0
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🐄 اختبار {i}: {test_case['name']}")
            print("-" * 40)
            
            try:
                response = requests.post(
                    'http://localhost:5000/suggest_mix_for_cow',
                    json={'requirements': test_case['data']},
                    headers={'Content-Type': 'application/json'},
                    timeout=10
                )
                
                if response.status_code == 200:
                    data = response.json()
                    
                    if 'suggested_mix' in data:
                        mix = data['suggested_mix']
                        
                        print(f"✅ نجح الاختبار {i}")
                        print(f"📊 عدد المكونات: {len(mix.get('mix_components', []))}")
                        print(f"💰 التكلفة الإجمالية: {mix.get('totals', {}).get('cost', 0)} د.أ")
                        print(f"⚖️ الوزن الإجمالي: {mix.get('totals', {}).get('weight', 0)} كغ")
                        
                        # عرض أهم المكونات
                        components = mix.get('mix_components', [])
                        if components:
                            print("🌾 أهم المكونات:")
                            for comp in components[:3]:  # أول 3 مكونات
                                ing = comp.get('ingredient', {})
                                print(f"   - {ing.get('name', 'غير محدد')}: {comp.get('quantity', 0)} كغ")
                        
                        success_count += 1
                    else:
                        print(f"❌ فشل الاختبار {i}: لم يتم العثور على الخلطة في الاستجابة")
                else:
                    print(f"❌ فشل الاختبار {i}: كود الاستجابة {response.status_code}")
                    
            except requests.exceptions.Timeout:
                print(f"⏰ فشل الاختبار {i}: انتهت مهلة الاتصال")
            except Exception as e:
                print(f"❌ فشل الاختبار {i}: {e}")
        
        # النتيجة النهائية
        print("\n" + "=" * 60)
        print(f"📊 النتيجة النهائية: {success_count}/{len(test_cases)} اختبار نجح")
        
        if success_count == len(test_cases):
            print("🎉 جميع الاختبارات نجحت! خاصية اقتراح الخلطة تعمل بشكل ممتاز")
            print("\n💻 كيفية الاستخدام:")
            print("1. افتح صفحة احتياجات البقرة: http://localhost:5000/cow_requirements")
            print("2. أدخل بيانات البقرة (الوزن، إنتاج الحليب، المرحلة)")
            print("3. اضغط 'حساب الاحتياجات'")
            print("4. اضغط 'اقتراح خلطة مناسبة'")
            print("5. راجع الخلطة المقترحة والتوصيات")
        elif success_count > 0:
            print(f"⚠️ بعض الاختبارات نجحت ({success_count}) - يحتاج لمراجعة")
        else:
            print("❌ جميع الاختبارات فشلت - يحتاج لإصلاح")
            
    except requests.exceptions.ConnectionError:
        print("❌ خطأ في الاتصال - تأكد من تشغيل البرنامج")
        print("💡 لتشغيل البرنامج:")
        print("   python run.py")
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")

if __name__ == '__main__':
    test_cow_mix_suggestion()
    print("=" * 60)
    print("🏁 انتهى الاختبار النهائي")