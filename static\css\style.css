/* إعدادات الخط العربي */
body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
}

/* تحسين شكل النصوص العربية */
.navbar-brand, .card-title, .form-label, h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
}

/* تحسين المسافات للنصوص العربية */
.form-control, .form-select {
    text-align: right;
    direction: rtl;
}

/* تحسين الجداول */
.table {
    direction: rtl;
}

.table th, .table td {
    text-align: right;
    vertical-align: middle;
}

/* تحسين الأزرار */
.btn {
    font-weight: 500;
}

/* تحسين البطاقات */
.card {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    overflow: hidden;
}

.card-header {
    font-weight: 600;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

/* تحسين النماذج */
.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* تحسين التنبيهات */
.alert {
    border-radius: 8px;
    font-weight: 500;
}

/* تحسين الأيقونات */
.fas, .far {
    margin-left: 5px;
}

/* تحسين شريط التنقل */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* تحسين التذييل */
footer {
    background-color: #f8f9fa !important;
    border-top: 1px solid #dee2e6;
    margin-top: auto;
}

/* تحسين الجداول المتجاوبة */
.table-responsive {
    border-radius: 8px;
    overflow: hidden;
}

/* تحسين المودال */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 15px 15px 0 0;
}

/* تحسين مؤشرات التحميل */
.spinner-border {
    width: 1.5rem;
    height: 1.5rem;
}

/* تحسين الرسوم البيانية */
.progress {
    height: 25px;
    border-radius: 15px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 15px;
    font-weight: 600;
    font-size: 0.875rem;
}

/* تحسين اختيار المكونات */
.form-select:focus, .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    transform: scale(1.02);
    transition: all 0.3s ease;
}

/* تحسين عرض معلومات المكون المختار */
.ingredient-info {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
}

/* تحسين جدول الخلطة */
#mixtureTable {
    border-radius: 10px;
    overflow: hidden;
}

#mixtureTable th {
    background: linear-gradient(135deg, #343a40, #495057) !important;
    color: white;
    font-weight: 600;
    text-align: center;
}

#mixtureTable tbody tr:hover {
    background-color: #f1f3f4;
    transform: scale(1.01);
    transition: all 0.2s ease;
}

/* شارات الفئات */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

/* تحسين المعاينة السريعة */
#quickPreview {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    border: 2px solid #0dcaf0;
    border-radius: 15px;
    animation: fadeIn 0.5s ease;
}

/* تحسين الأزرار */
.btn-lg {
    padding: 12px 24px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 10px;
}

/* تأثيرات خاصة للمكونات */
.ingredient-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    margin: 10px 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* تحسين عداد المكونات */
#mixtureCount {
    background: linear-gradient(135deg, #ffc107, #ffca2c);
    color: #000;
    padding: 2px 8px;
    border-radius: 20px;
    font-weight: 700;
    font-size: 0.9rem;
}

/* تحسين الحقول الرقمية */
input[type="number"] {
    text-align: center;
    font-weight: 600;
}

/* تأثير النبض للأزرار المهمة */
.btn-primary:not(:disabled) {
    animation: pulse-primary 2s infinite;
}

@keyframes pulse-primary {
    0% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(13, 110, 253, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0);
    }
}

/* تحسين عرض الأيقونات */
.fas, .far {
    margin-left: 5px;
    transition: transform 0.2s ease;
}

.btn:hover .fas, .btn:hover .far {
    transform: scale(1.1);
}

/* تحسين الجداول المتجاوبة */
@media (max-width: 992px) {
    .table-responsive {
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .form-control, .form-select {
        font-size: 16px; /* منع الزوم في الهواتف */
    }
}

/* تحسين الرسائل والتنبيهات */
.alert {
    border-radius: 15px;
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    font-weight: 500;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1, #b8daff);
    color: #0c5460;
}

/* تحسين المودال */
.modal-content {
    border-radius: 20px;
    border: none;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
    border-radius: 20px 20px 0 0;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

/* تحسينات خاصة بصفحة احتياجات البقرة */
.cow-icon {
    font-size: 4rem;
    color: #6c757d;
    animation: cowBounce 2s infinite;
}

@keyframes cowBounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

/* تحسين بطاقات النتائج */
.result-card {
    transition: all 0.3s ease;
    border-radius: 15px;
    overflow: hidden;
}

.result-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* تدرجات لونية للبطاقات */
.bg-primary-gradient {
    background: linear-gradient(135deg, #0d6efd, #6610f2);
}

.bg-danger-gradient {
    background: linear-gradient(135deg, #dc3545, #fd7e14);
}

.bg-warning-gradient {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.bg-success-gradient {
    background: linear-gradient(135deg, #198754, #20c997);
}

/* تحسين النماذج في صفحة البقرة */
.form-control-lg:focus, .form-select-lg:focus {
    border-color: #198754;
    box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
}

/* تحسين الجداول الصغيرة */
.table-sm td {
    padding: 0.5rem;
    vertical-align: middle;
}

.table-sm .fw-bold {
    color: #495057;
    font-weight: 600;
}

/* تأثيرات خاصة للأرقام */
.number-highlight {
    font-size: 1.1em;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* تحسين أيقونة البقرة */
.fa-cow {
    color: #8b4513;
}

/* تحسين تدرج الخلفية للبطاقات المميزة */
.gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.gradient-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* تحسين النصوص في البطاقات الملونة */
.card.text-white h4, .card.text-white h5, .card.text-white h6 {
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* تحسين الزر المتحرك */
.btn-calculate {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
    transform: translateY(0);
    transition: all 0.3s ease;
}

.btn-calculate:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

/* تحسين عرض النتائج */
.requirements-summary {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 20px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* تحسين الجداول في صفحة البقرة */
.table-cow {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.table-cow th {
    background: linear-gradient(135deg, #495057, #6c757d);
    color: white;
    font-weight: 600;
    border: none;
}

.table-cow td {
    border-color: #dee2e6;
    padding: 12px;
}

/* تحسين المعلومات المساعدة */
.help-info {
    background: linear-gradient(135deg, #d1ecf1, #b8daff);
    border-left: 4px solid #0dcaf0;
    border-radius: 0 10px 10px 0;
}

/* تأثيرات الأرقام المتحركة */
.animated-number {
    animation: numberPulse 1.5s ease-in-out;
}

@keyframes numberPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* تحسين العناصر التفاعلية */
.btn:hover {
    transform: translateY(-1px);
    transition: transform 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    transition: transform 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* تحسين الجداول للشاشات الصغيرة */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-group .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
}

/* ألوان مخصصة للقيم الغذائية */
.protein-color {
    color: #dc3545;
    font-weight: 600;
}

.energy-color {
    color: #fd7e14;
    font-weight: 600;
}

.fiber-color {
    color: #198754;
    font-weight: 600;
}

.cost-color {
    color: #6610f2;
    font-weight: 600;
}

/* تحسين عرض النتائج */
.results-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 2px solid #dee2e6;
}

/* تحسين أيقونات الحالة */
.status-icon {
    font-size: 1.2rem;
    margin-left: 8px;
}

/* تحسين عرض الأرقام */
.number-display {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    padding: 2px 6px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

/* تحسين الرسائل الفارغة */
.empty-state {
    padding: 3rem 1rem;
    text-align: center;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* تحسين طباعة التقارير */
@media print {
    body {
        font-size: 12px;
        line-height: 1.4;
    }
    
    .no-print {
        display: none !important;
    }
    
    .card {
        page-break-inside: avoid;
        margin-bottom: 1rem;
    }
    
    .table {
        font-size: 11px;
    }
    
    .table th, .table td {
        padding: 0.3rem;
    }
}

/* تحسين العناصر المتحركة */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تحسين شريط التقدم للقيم الغذائية */
.nutrition-bar {
    height: 20px;
    border-radius: 10px;
    overflow: hidden;
    background-color: #e9ecef;
    margin: 5px 0;
}

.nutrition-fill {
    height: 100%;
    transition: width 0.6s ease;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
    color: white;
}

/* ألوان للقيم الغذائية المختلفة */
.protein-fill { background: linear-gradient(135deg, #dc3545, #b02a37); }
.energy-fill { background: linear-gradient(135deg, #fd7e14, #e8681b); }
.fiber-fill { background: linear-gradient(135deg, #198754, #146c43); }