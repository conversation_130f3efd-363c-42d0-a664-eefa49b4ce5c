#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إضافة بيانات تجريبية مباشرة لقاعدة البيانات
"""

import sqlite3
from datetime import datetime, timedelta
import random

def add_sample_cow_data():
    """إضافة بيانات أبقار تجريبية"""
    print("🐄 إضافة بيانات تجريبية للأبقار...")
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('feed_mixer.db')
    
    # بيانات أبقار تجريبية (مع مستوى النشاط)
    sample_cows = [
        ('فاطمة', 550, 28, 'mid', 'moderate', 24.5, 1920, 29.2, 9.8),
        ('خديجة', 480, 22, 'early', 'moderate', 21.8, 1680, 25.8, 8.7),
        ('عائشة', 620, 35, 'mid', 'high', 28.2, 2280, 33.5, 11.3),
        ('زينب', 500, 18, 'late', 'low', 19.5, 1450, 22.8, 7.8),
        ('مريم', 580, 30, 'mid', 'moderate', 26.1, 2050, 31.2, 10.4),
        ('حفصة', 460, 20, 'early', 'moderate', 20.2, 1580, 24.1, 8.1),
        ('أمينة', 520, 25, 'mid', 'moderate', 23.2, 1850, 27.5, 9.2),
        ('صفية', 490, 19, 'late', 'low', 18.8, 1420, 21.9, 7.5)
    ]
    
    # حذف البيانات القديمة
    conn.execute('DELETE FROM cow_calculations')
    
    # إدراج البيانات الجديدة
    for i, cow_data in enumerate(sample_cows):
        # تاريخ متنوع
        date_offset = timedelta(days=random.randint(1, 30), hours=random.randint(0, 23))
        calc_date = (datetime.now() - date_offset).strftime('%Y-%m-%d %H:%M:%S')
        
        conn.execute('''
            INSERT INTO cow_calculations 
            (cow_name, weight, milk_production, lactation_stage, activity_level, total_dm, 
             total_protein, total_energy, daily_cost, calculation_date)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', cow_data + (calc_date,))
    
    conn.commit()
    
    # التحقق من البيانات
    count = conn.execute('SELECT COUNT(*) FROM cow_calculations').fetchone()[0]
    print(f"✅ تم إدراج {count} سجل بقرة")
    
    # عرض عينة من البيانات
    print("\n📊 عينة من البيانات المضافة:")
    rows = conn.execute('''
        SELECT cow_name, weight, milk_production, daily_cost 
        FROM cow_calculations 
        ORDER BY calculation_date DESC 
        LIMIT 5
    ''').fetchall()
    
    for row in rows:
        print(f"   - {row[0]}: {row[1]}كغ، {row[2]}ل/يوم، {row[3]}د.أ/يوم")
    
    conn.close()

def test_database():
    """فحص قاعدة البيانات"""
    print("\n🔍 فحص قاعدة البيانات...")
    
    try:
        conn = sqlite3.connect('feed_mixer.db')
        
        # فحص الجداول
        tables = conn.execute('''
            SELECT name FROM sqlite_master 
            WHERE type='table' 
            ORDER BY name
        ''').fetchall()
        
        print(f"📁 الجداول الموجودة ({len(tables)}):")
        for table in tables:
            count = conn.execute(f'SELECT COUNT(*) FROM {table[0]}').fetchone()[0]
            print(f"   - {table[0]}: {count} سجل")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")

def main():
    """الدالة الرئيسية"""
    print("🗃️ إعداد بيانات تجريبية لمقارنة الأبقار")
    print("=" * 50)
    
    add_sample_cow_data()
    test_database()
    
    print("\n" + "=" * 50)
    print("✅ تم إعداد البيانات بنجاح!")
    print("\n📱 يمكنك الآن:")
    print("1. فتح http://localhost:5000/compare_cows")
    print("2. اختيار أبقار للمقارنة")
    print("3. عرض النتائج والإحصائيات")
    print("4. مقارنة الكفاءة والتكلفة")

if __name__ == "__main__":
    main()