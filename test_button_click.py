#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_button_functionality():
    """اختبار وظيفة زر حساب الاحتياجات مباشرة"""
    
    base_url = "http://localhost:5000"
    
    print("🧪 اختبار وظيفة زر حساب الاحتياجات...")
    print("=" * 50)
    
    # بيانات البقرة للاختبار (نفس البيانات المدخلة في الصفحة)
    test_data = {
        "animal_type": "cow",
        "cow_weight": 500,  # نفس القيمة في الصفحة
        "cow_milk": 25,     # نفس القيمة في الصفحة
        "cow_stage": "mid", # نفس القيمة في الصفحة
        "activity_level": "moderate"  # نفس القيمة في الصفحة
    }
    
    print("📊 البيانات المرسلة:")
    print(f"   🐄 وزن البقرة: {test_data['cow_weight']} كغ")
    print(f"   🥛 إنتاج الحليب: {test_data['cow_milk']} لتر/يوم")
    print(f"   📅 مرحلة الإنتاج: {test_data['cow_stage']}")
    print(f"   🏃 مستوى النشاط: {test_data['activity_level']}")
    
    try:
        print("\n🌐 إرسال الطلب إلى API...")
        response = requests.post(f"{base_url}/calculate_animal_requirements", 
                               json=test_data,
                               headers={'Content-Type': 'application/json'})
        
        print(f"📡 كود الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ تم استقبال البيانات بنجاح!")
            
            if 'requirements' in data:
                req = data['requirements']
                
                print("\n📋 النتائج المحسوبة:")
                print("=" * 30)
                print(f"📊 المادة الجافة: {req.get('total_dm', 'غير محدد')} كغ/يوم")
                print(f"🥩 البروتين: {req.get('total_protein', 'غير محدد')} غرام/يوم")
                print(f"⚡ الطاقة: {req.get('total_energy', 'غير محدد')} Mcal/يوم")
                print(f"📈 نسبة البروتين: {req.get('protein_percentage', 'غير محدد')}%")
                print(f"💰 التكلفة اليومية: {req.get('daily_cost', 'غير محدد')} ريال")
                
                if 'feed_distribution' in req:
                    feed_dist = req['feed_distribution']
                    print(f"\n🌾 توزيع العلف:")
                    print(f"   🌽 علف مركز: {feed_dist.get('concentrate', 'غير محدد')} كغ")
                    print(f"   🌿 علف خشن: {feed_dist.get('roughage', 'غير محدد')} كغ")
                    print(f"   💊 إضافات: {feed_dist.get('supplement', 'غير محدد')} كغ")
                
                # اختبار اقتراح الخلطة
                print("\n🧪 اختبار اقتراح الخلطة...")
                mix_response = requests.post(f"{base_url}/suggest_mix_for_cow",
                                           json={'requirements': req},
                                           headers={'Content-Type': 'application/json'})
                
                if mix_response.status_code == 200:
                    mix_data = mix_response.json()
                    if 'suggested_mix' in mix_data:
                        print("✅ اقتراح الخلطة يعمل أيضاً!")
                        mix = mix_data['suggested_mix']
                        print(f"💰 تكلفة الخلطة: {mix['totals']['cost']} ريال")
                        print(f"📊 عدد المكونات: {len(mix['mix_components'])}")
                    else:
                        print("❌ مشكلة في اقتراح الخلطة")
                else:
                    print(f"❌ فشل في اقتراح الخلطة: {mix_response.status_code}")
                
                return True
            else:
                print("❌ البيانات المرجعة لا تحتوي على 'requirements'")
                print(f"📝 البيانات المرجعة: {data}")
                return False
        else:
            print(f"❌ فشل في الطلب: {response.status_code}")
            print(f"📝 رسالة الخطأ: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def test_page_access():
    """اختبار الوصول للصفحة"""
    
    base_url = "http://localhost:5000"
    
    print("\n🌐 اختبار الوصول للصفحة...")
    
    try:
        response = requests.get(f"{base_url}/cow_requirements")
        
        if response.status_code == 200:
            print("✅ الصفحة متاحة")
            
            # التحقق من وجود العناصر المهمة
            content = response.text
            
            if 'calculateBtn' in content:
                print("✅ زر حساب الاحتياجات موجود")
            else:
                print("❌ زر حساب الاحتياجات مفقود")
                
            if 'fetch(\'/calculate_animal_requirements\'' in content:
                print("✅ استدعاء API موجود في JavaScript")
            else:
                print("❌ استدعاء API مفقود في JavaScript")
                
            return True
        else:
            print(f"❌ فشل في الوصول للصفحة: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الوصول للصفحة: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار شامل لوظيفة حساب الاحتياجات...")
    print("🎯 الهدف: التأكد من أن كل شيء يعمل بشكل صحيح")
    print()
    
    # اختبار الوصول للصفحة
    page_ok = test_page_access()
    
    # اختبار وظيفة API
    api_ok = test_button_functionality()
    
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج النهائي:")
    print(f"   الصفحة: {'✅ تعمل' if page_ok else '❌ لا تعمل'}")
    print(f"   API: {'✅ يعمل' if api_ok else '❌ لا يعمل'}")
    
    if page_ok and api_ok:
        print("\n🎉 جميع الوظائف تعمل بشكل مثالي!")
        print("💡 الآن يمكنك:")
        print("   1. فتح الصفحة: http://localhost:5000/cow_requirements")
        print("   2. إدخال البيانات (الوزن: 500، الحليب: 25)")
        print("   3. الضغط على 'حساب الاحتياجات'")
        print("   4. مشاهدة النتائج تظهر فوراً!")
        print("   5. الضغط على 'اقتراح خلطة مناسبة'")
    else:
        print("\n⚠️ هناك مشاكل تحتاج إصلاح")
        
    print(f"\n🌐 رابط الصفحة: http://localhost:5000/cow_requirements")
