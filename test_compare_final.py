#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نهائي لصفحة مقارنة الأبقار
"""

def test_compare_cows_page():
    """اختبار صفحة مقارنة الأبقار"""
    print("🔧 اختبار صفحة مقارنة الأبقار...")
    
    try:
        import urllib.request
        import urllib.error
        
        # اختبار الصفحة
        url = "http://localhost:5000/compare_cows"
        
        try:
            with urllib.request.urlopen(url, timeout=10) as response:
                if response.status == 200:
                    content = response.read().decode('utf-8')
                    print("✅ صفحة مقارنة الأبقار تعمل بنجاح!")
                    
                    # فحص المحتوى
                    if "مقارنة الأبقار" in content:
                        print("✅ العنوان موجود")
                    if "فاطمة" in content or "خديجة" in content:
                        print("✅ البيانات التجريبية موجودة")
                    if "compareBtn" in content:
                        print("✅ زر المقارنة موجود")
                    
                    return True
                else:
                    print(f"❌ كود الاستجابة: {response.status}")
                    return False
                    
        except urllib.error.URLError as e:
            print(f"❌ خطأ في الاتصال: {e}")
            print("تأكد من أن البرنامج يعمل على http://localhost:5000")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_other_pages():
    """اختبار الصفحات الأخرى"""
    print("\n🔧 اختبار الصفحات الأخرى...")
    
    pages = [
        ("الصفحة الرئيسية", "http://localhost:5000/"),
        ("احتياجات البقرة", "http://localhost:5000/cow_requirements"),
        ("احتياجات الحيوانات", "http://localhost:5000/animal_requirements"),
        ("تاريخ الأبقار", "http://localhost:5000/cow_history"),
        ("الخلطات المحفوظة", "http://localhost:5000/saved_mixes"),
    ]
    
    working_pages = 0
    
    for name, url in pages:
        try:
            import urllib.request
            with urllib.request.urlopen(url, timeout=5) as response:
                if response.status == 200:
                    print(f"✅ {name}: يعمل")
                    working_pages += 1
                else:
                    print(f"❌ {name}: كود {response.status}")
        except Exception as e:
            print(f"❌ {name}: خطأ في الاتصال")
    
    print(f"\n📊 النتيجة: {working_pages}/{len(pages)} صفحة تعمل")
    return working_pages == len(pages)

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار مقارنة الأبقار - النسخة النهائية")
    print("=" * 60)
    
    # اختبار صفحة المقارنة
    compare_works = test_compare_cows_page()
    
    # اختبار الصفحات الأخرى
    all_works = test_other_pages()
    
    print("\n" + "=" * 60)
    
    if compare_works:
        print("🎉 مقارنة الأبقار تعمل بنجاح!")
        print("\n💻 كيفية الاستخدام:")
        print("1. افتح http://localhost:5000/compare_cows")
        print("2. ستجد قائمة بالأبقار التجريبية")
        print("3. اختر أبقار متعددة بوضع علامة ✓")
        print("4. اضغط 'مقارنة الأبقار المختارة'")
        print("5. راجع النتائج والتحليلات")
    else:
        print("❌ مقارنة الأبقار لا تعمل")
        print("🔧 تأكد من:")
        print("- تشغيل البرنامج: python run.py")
        print("- وجود البيانات التجريبية")
        print("- عدم وجود أخطاء في الكود")
    
    if all_works:
        print("\n🌟 جميع الصفحات تعمل بنجاح!")
    else:
        print("\n⚠️ بعض الصفحات لا تعمل")

if __name__ == "__main__":
    main()