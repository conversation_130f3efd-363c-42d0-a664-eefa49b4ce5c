#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار خاص لحساب احتياجات البقرة الحلوب
"""

from app_simple import app, init_db

def test_cow_calculation():
    """اختبار حساب احتياجات البقرة الحلوب"""
    print("🧪 اختبار حساب احتياجات البقرة الحلوب")
    print("=" * 50)
    
    # بيانات تجريبية للبقرة
    test_data = {
        'weight': 550,
        'milk_production': 25,
        'lactation_stage': 'mid',
        'activity_level': 'moderate'
    }
    
    with app.test_client() as client:
        try:
            # اختبار حساب الاحتياجات
            response = client.post('/calculate_animal_requirements', 
                json={
                    'animal_type': 'cow',
                    'cow_weight': test_data['weight'],
                    'cow_milk': test_data['milk_production'],
                    'cow_stage': test_data['lactation_stage'],
                    'activity_level': test_data['activity_level']
                },
                content_type='application/json'
            )
            
            if response.status_code == 200:
                data = response.get_json()
                print(f"✅ حساب الاحتياجات يعمل بنجاح!")
                print(f"📊 النتائج:")
                print(f"   - المادة الجافة: {data.get('total_dm', 'غير محدد')} كغ/يوم")
                print(f"   - البروتين: {data.get('total_protein', 'غير محدد')} غرام/يوم")
                print(f"   - الطاقة: {data.get('total_energy', 'غير محدد')} Mcal/يوم")
                print(f"   - التكلفة اليومية: {data.get('daily_cost', 'غير محدد')} د.أ")
                
                # اختبار اقتراح الخلطة
                print(f"\n🔍 اختبار اقتراح الخلطة...")
                mix_response = client.post('/suggest_mix_for_animal',
                    json={
                        'animal_type': 'cow',
                        'requirements': data
                    },
                    content_type='application/json'
                )
                
                if mix_response.status_code == 200:
                    mix_data = mix_response.get_json()
                    if 'suggested_mix' in mix_data:
                        mix = mix_data['suggested_mix']
                        print(f"✅ اقتراح الخلطة يعمل بنجاح!")
                        print(f"   - عدد المكونات: {len(mix['mix_components'])}")
                        print(f"   - التكلفة: {mix['totals']['cost']} د.أ")
                        print(f"   - دقة البروتين: {mix['quality_indicators']['protein_match']}%")
                        print(f"   - دقة الطاقة: {mix['quality_indicators']['energy_match']}%")
                        print(f"   - كفاءة التكلفة: {mix['quality_indicators']['cost_efficiency']}%")
                        
                        print(f"\n📋 مكونات الخلطة:")
                        for i, comp in enumerate(mix['mix_components'], 1):
                            print(f"   {i}. {comp['ingredient']['name']}: {comp['quantity']:.2f} كغ ({comp['percentage']:.1f}%)")
                        
                        print(f"\n💡 التوصيات:")
                        for rec in mix['recommendations']:
                            print(f"   - {rec['message']}: {rec['suggestion']}")
                    else:
                        print(f"❌ خطأ في اقتراح الخلطة: {mix_data}")
                else:
                    print(f"❌ خطأ في طلب اقتراح الخلطة: {mix_response.status_code}")
                    
            else:
                print(f"❌ خطأ في حساب الاحتياجات: {response.status_code}")
                print(f"   الاستجابة: {response.get_data(as_text=True)}")
                
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 انتهى اختبار البقرة الحلوب!")

if __name__ == "__main__":
    init_db()
    test_cow_calculation()