#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشخيص مشكلة حساب البقرة الحلوب
"""

from app_simple import app, init_db
import json

def test_cow_debug():
    """اختبار تفصيلي لمعرفة مكان المشكلة"""
    print("🔧 تشخيص مشكلة حساب البقرة الحلوب")
    print("=" * 50)
    
    # التأكد من أن قاعدة البيانات مهيأة
    print("1. تهيئة قاعدة البيانات...")
    init_db()
    print("✅ تم تهيئة قاعدة البيانات")
    
    # اختبار البيانات المدخلة
    test_inputs = {
        'weight': 550,
        'milk': 25,
        'stage': 'mid',
        'activity': 'moderate'
    }
    
    print(f"2. البيانات المختبرة: {test_inputs}")
    
    # محاولة الحساب عبر الـ API
    with app.test_client() as client:
        print("3. اختبار الحساب عبر API...")
        
        response = client.post('/calculate_animal_requirements', 
            json={
                'animal_type': 'cow',
                'cow_weight': test_inputs['weight'],
                'cow_milk': test_inputs['milk'],
                'cow_stage': test_inputs['stage'],
                'activity_level': test_inputs['activity']
            },
            content_type='application/json'
        )
        
        print(f"   - كود الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            data = response.get_json()
            print("✅ حساب API يعمل بنجاح!")
            print(f"   - المادة الجافة: {data.get('total_dm')} كغ")
            print(f"   - البروتين: {data.get('total_protein')} غرام")
            print(f"   - الطاقة: {data.get('total_energy')} Mcal")
            print(f"   - التكلفة: {data.get('daily_cost')} د.أ")
            
            # اختبار اقتراح الخلطة
            print("\n4. اختبار اقتراح الخلطة...")
            mix_response = client.post('/suggest_mix_for_animal',
                json={
                    'animal_type': 'cow',
                    'requirements': data
                },
                content_type='application/json'
            )
            
            if mix_response.status_code == 200:
                mix_data = mix_response.get_json()
                print("✅ اقتراح الخلطة يعمل!")
                print(f"   - عدد المكونات: {len(mix_data['suggested_mix']['mix_components'])}")
                
            else:
                print(f"❌ فشل اقتراح الخلطة: {mix_response.status_code}")
                
        else:
            print(f"❌ فشل حساب API: {response.status_code}")
            print(f"   الرسالة: {response.get_data(as_text=True)}")
    
    # اختبار صفحة الحيوانات
    print("\n5. اختبار صفحة الحيوانات...")
    with app.test_client() as client:
        response = client.get('/animal_requirements')
        if response.status_code == 200:
            print("✅ صفحة الحيوانات تحمل بنجاح")
        else:
            print(f"❌ فشل تحميل صفحة الحيوانات: {response.status_code}")
    
    # تحقق من المكونات في قاعدة البيانات
    print("\n6. فحص قاعدة البيانات...")
    from app_simple import get_db
    conn = get_db()
    
    # عدد المكونات
    cursor = conn.execute('SELECT COUNT(*) FROM ingredients')
    ingredients_count = cursor.fetchone()[0]
    print(f"   - عدد المكونات: {ingredients_count}")
    
    # المكونات الخشنة
    cursor = conn.execute('SELECT name, fiber FROM ingredients WHERE fiber >= 20 ORDER BY fiber DESC')
    roughage = cursor.fetchall()
    print(f"   - الأعلاف الخشنة ({len(roughage)}):")
    for item in roughage[:5]:  # أول 5
        print(f"     * {item[0]}: {item[1]}% ألياف")
    
    # مصادر البروتين
    cursor = conn.execute('SELECT name, protein FROM ingredients WHERE protein >= 20 ORDER BY protein DESC')
    proteins = cursor.fetchall()
    print(f"   - مصادر البروتين ({len(proteins)}):")
    for item in proteins[:5]:  # أول 5
        print(f"     * {item[0]}: {item[1]}% بروتين")
    
    conn.close()
    
    print("\n" + "=" * 50)
    print("🎯 التشخيص مكتمل!")
    
    # التوصيات
    print("\n💡 التوصيات:")
    print("1. تأكد من أن الخادم يعمل على المنفذ 5000")
    print("2. افتح المتصفح على http://localhost:5000/animal_requirements")
    print("3. ادخل بيانات البقرة واضغط 'حساب الاحتياجات'")
    print("4. تحقق من وحدة التحكم في المتصفح (F12) للأخطاء")

if __name__ == "__main__":
    test_cow_debug()