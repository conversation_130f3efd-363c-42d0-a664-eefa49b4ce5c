('C:\\Users\\<USER>\\Desktop\\New folder (5)\\build\\feed_mixer\\PYZ-00.pyz',
 [('__future__',
   'C:\\Program Files\\Python313\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Program Files\\Python313\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_colorize', 'C:\\Program Files\\Python313\\Lib\\_colorize.py', 'PYMODULE'),
  ('_compat_pickle',
   'C:\\Program Files\\Python313\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Program Files\\Python313\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_ios_support',
   'C:\\Program Files\\Python313\\Lib\\_ios_support.py',
   'PYMODULE'),
  ('_opcode_metadata',
   'C:\\Program Files\\Python313\\Lib\\_opcode_metadata.py',
   'PYMODULE'),
  ('_py_abc', 'C:\\Program Files\\Python313\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime',
   'C:\\Program Files\\Python313\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Program Files\\Python313\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyrepl',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\__init__.py',
   'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.base_eventqueue',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\base_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.commands',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\commands.py',
   'PYMODULE'),
  ('_pyrepl.completing_reader',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('_pyrepl.console',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\console.py',
   'PYMODULE'),
  ('_pyrepl.curses',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\curses.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.historical_reader',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.input',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\input.py',
   'PYMODULE'),
  ('_pyrepl.keymap',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\keymap.py',
   'PYMODULE'),
  ('_pyrepl.main',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\main.py',
   'PYMODULE'),
  ('_pyrepl.pager',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\pager.py',
   'PYMODULE'),
  ('_pyrepl.reader',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\reader.py',
   'PYMODULE'),
  ('_pyrepl.readline',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\readline.py',
   'PYMODULE'),
  ('_pyrepl.simple_interact',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.trace',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\trace.py',
   'PYMODULE'),
  ('_pyrepl.types',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\types.py',
   'PYMODULE'),
  ('_pyrepl.unix_console',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.utils',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\utils.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('_pyrepl.windows_eventqueue',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\windows_eventqueue.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Program Files\\Python313\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime', 'C:\\Program Files\\Python313\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Program Files\\Python313\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('app_simple',
   'C:\\Users\\<USER>\\Desktop\\New folder (5)\\app_simple.py',
   'PYMODULE'),
  ('argparse', 'C:\\Program Files\\Python313\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Program Files\\Python313\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'C:\\Program Files\\Python313\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'C:\\Program Files\\Python313\\Lib\\bisect.py', 'PYMODULE'),
  ('blinker',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\blinker\\__init__.py',
   'PYMODULE'),
  ('blinker._utilities',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\blinker\\_utilities.py',
   'PYMODULE'),
  ('blinker.base',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\blinker\\base.py',
   'PYMODULE'),
  ('bz2', 'C:\\Program Files\\Python313\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\Program Files\\Python313\\Lib\\calendar.py', 'PYMODULE'),
  ('click',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click._compat',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.testing',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('click.types',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('code', 'C:\\Program Files\\Python313\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Program Files\\Python313\\Lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Program Files\\Python313\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Program Files\\Python313\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Program Files\\Python313\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Program Files\\Python313\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Program Files\\Python313\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Program Files\\Python313\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Program Files\\Python313\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Program Files\\Python313\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'C:\\Program Files\\Python313\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'C:\\Program Files\\Python313\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('curses',
   'C:\\Program Files\\Python313\\Lib\\curses\\__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   'C:\\Program Files\\Python313\\Lib\\curses\\has_key.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Program Files\\Python313\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'C:\\Program Files\\Python313\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'C:\\Program Files\\Python313\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'C:\\Program Files\\Python313\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'C:\\Program Files\\Python313\\Lib\\dis.py', 'PYMODULE'),
  ('email',
   'C:\\Program Files\\Python313\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Program Files\\Python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Program Files\\Python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Program Files\\Python313\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Program Files\\Python313\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Program Files\\Python313\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Program Files\\Python313\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Program Files\\Python313\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Program Files\\Python313\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Program Files\\Python313\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Program Files\\Python313\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Program Files\\Python313\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Program Files\\Python313\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Program Files\\Python313\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Program Files\\Python313\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Program Files\\Python313\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Program Files\\Python313\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Program Files\\Python313\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Program Files\\Python313\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Program Files\\Python313\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('flask',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('flask.app',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask\\app.py',
   'PYMODULE'),
  ('flask.blueprints',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.cli',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask\\cli.py',
   'PYMODULE'),
  ('flask.config',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.ctx',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask\\ctx.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.globals',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.helpers',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.json',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE'),
  ('flask.json.provider',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('flask.json.tag',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('flask.logging',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('flask.sansio', '-', 'PYMODULE'),
  ('flask.sansio.app',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask\\sansio\\app.py',
   'PYMODULE'),
  ('flask.sansio.blueprints',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask\\sansio\\blueprints.py',
   'PYMODULE'),
  ('flask.sansio.scaffold',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask\\sansio\\scaffold.py',
   'PYMODULE'),
  ('flask.sessions',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.signals',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('flask.templating',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.testing',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('flask.typing',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.wrappers',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('fnmatch', 'C:\\Program Files\\Python313\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\Program Files\\Python313\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'C:\\Program Files\\Python313\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'C:\\Program Files\\Python313\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\Program Files\\Python313\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\Program Files\\Python313\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'C:\\Program Files\\Python313\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'C:\\Program Files\\Python313\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\Program Files\\Python313\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'C:\\Program Files\\Python313\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'C:\\Program Files\\Python313\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'C:\\Program Files\\Python313\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http', 'C:\\Program Files\\Python313\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'C:\\Program Files\\Python313\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Program Files\\Python313\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Program Files\\Python313\\Lib\\http\\server.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Program Files\\Python313\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Program Files\\Python313\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Program Files\\Python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Program Files\\Python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Program Files\\Python313\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Program Files\\Python313\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Program Files\\Python313\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Program Files\\Python313\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'C:\\Program Files\\Python313\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Program Files\\Python313\\Lib\\ipaddress.py', 'PYMODULE'),
  ('itsdangerous',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jinja2',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json', 'C:\\Program Files\\Python313\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'C:\\Program Files\\Python313\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Program Files\\Python313\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Program Files\\Python313\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Program Files\\Python313\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'C:\\Program Files\\Python313\\Lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes', 'C:\\Program Files\\Python313\\Lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'C:\\Program Files\\Python313\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path',
   'C:\\Program Files\\Python313\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers', 'C:\\Program Files\\Python313\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'C:\\Program Files\\Python313\\Lib\\opcode.py', 'PYMODULE'),
  ('packaging',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Program Files\\Python313\\Lib\\pathlib\\__init__.py',
   'PYMODULE'),
  ('pathlib._abc',
   'C:\\Program Files\\Python313\\Lib\\pathlib\\_abc.py',
   'PYMODULE'),
  ('pathlib._local',
   'C:\\Program Files\\Python313\\Lib\\pathlib\\_local.py',
   'PYMODULE'),
  ('pickle', 'C:\\Program Files\\Python313\\Lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Program Files\\Python313\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'C:\\Program Files\\Python313\\Lib\\platform.py', 'PYMODULE'),
  ('pprint', 'C:\\Program Files\\Python313\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile',
   'C:\\Program Files\\Python313\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc', 'C:\\Program Files\\Python313\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'C:\\Program Files\\Python313\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Program Files\\Python313\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('queue', 'C:\\Program Files\\Python313\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'C:\\Program Files\\Python313\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Program Files\\Python313\\Lib\\random.py', 'PYMODULE'),
  ('rlcompleter',
   'C:\\Program Files\\Python313\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy', 'C:\\Program Files\\Python313\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'C:\\Program Files\\Python313\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'C:\\Program Files\\Python313\\Lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'C:\\Program Files\\Python313\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'C:\\Program Files\\Python313\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Program Files\\Python313\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'C:\\Program Files\\Python313\\Lib\\site.py', 'PYMODULE'),
  ('socket', 'C:\\Program Files\\Python313\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'C:\\Program Files\\Python313\\Lib\\socketserver.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Program Files\\Python313\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   'C:\\Program Files\\Python313\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Program Files\\Python313\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Program Files\\Python313\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl', 'C:\\Program Files\\Python313\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics',
   'C:\\Program Files\\Python313\\Lib\\statistics.py',
   'PYMODULE'),
  ('string', 'C:\\Program Files\\Python313\\Lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'C:\\Program Files\\Python313\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Program Files\\Python313\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Program Files\\Python313\\Lib\\sysconfig\\__init__.py',
   'PYMODULE'),
  ('tarfile', 'C:\\Program Files\\Python313\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Program Files\\Python313\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Program Files\\Python313\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Program Files\\Python313\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'C:\\Program Files\\Python313\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Program Files\\Python313\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib',
   'C:\\Program Files\\Python313\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'C:\\Program Files\\Python313\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'C:\\Program Files\\Python313\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'C:\\Program Files\\Python313\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Program Files\\Python313\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'C:\\Program Files\\Python313\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'C:\\Program Files\\Python313\\Lib\\typing.py', 'PYMODULE'),
  ('unittest',
   'C:\\Program Files\\Python313\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Program Files\\Python313\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Program Files\\Python313\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Program Files\\Python313\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Program Files\\Python313\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Program Files\\Python313\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Program Files\\Python313\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Program Files\\Python313\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Program Files\\Python313\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Program Files\\Python313\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Program Files\\Python313\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Program Files\\Python313\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Program Files\\Python313\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Program Files\\Python313\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Program Files\\Python313\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Program Files\\Python313\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Program Files\\Python313\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('uuid', 'C:\\Program Files\\Python313\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser',
   'C:\\Program Files\\Python313\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('werkzeug',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\accept.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\structures.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.http',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.local',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug.test',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'c:\\Users\\<USER>\\Desktop\\New folder '
   '(5)\\.venv\\Lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('xml', 'C:\\Program Files\\Python313\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.parsers',
   'C:\\Program Files\\Python313\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Program Files\\Python313\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Program Files\\Python313\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Program Files\\Python313\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Program Files\\Python313\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Program Files\\Python313\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Program Files\\Python313\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Program Files\\Python313\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Program Files\\Python313\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Program Files\\Python313\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Program Files\\Python313\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Program Files\\Python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Program Files\\Python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'C:\\Program Files\\Python313\\Lib\\zipimport.py', 'PYMODULE')])
