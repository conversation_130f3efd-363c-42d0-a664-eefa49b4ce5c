#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار خاص لميزة اقتراح الخلطة للحيوانات المختلفة
"""

from app_simple import app, init_db

def test_animal_mix_suggestions():
    """اختبار اقتراح الخلطة لجميع أنواع الحيوانات"""
    print("🧪 اختبار ميزة اقتراح الخلطة للحيوانات المختلفة")
    print("=" * 60)
    
    # تحضير بيانات تجريبية
    test_animals = {
        'cow': {
            'total_dm': 20.0,
            'total_protein': 2800,
            'total_energy': 32.0,
            'daily_cost': 8.5
        },
        'calf': {
            'total_dm': 4.5,
            'total_protein': 900,
            'total_energy': 12.0,
            'daily_cost': 3.2
        },
        'sheep': {
            'total_dm': 1.8,
            'total_protein': 240,
            'total_energy': 4.2,
            'daily_cost': 1.1
        },
        'goat': {
            'total_dm': 2.2,
            'total_protein': 280,
            'total_energy': 5.1,
            'daily_cost': 1.4
        },
        'buffalo': {
            'total_dm': 24.0,
            'total_protein': 3200,
            'total_energy': 35.0,
            'daily_cost': 9.8
        }
    }
    
    with app.test_client() as client:
        for animal_type, requirements in test_animals.items():
            print(f"\n🔍 اختبار {animal_type}...")
            
            try:
                response = client.post('/suggest_mix_for_animal', 
                    json={
                        'animal_type': animal_type,
                        'requirements': requirements
                    },
                    content_type='application/json'
                )
                
                if response.status_code == 200:
                    data = response.get_json()
                    if 'suggested_mix' in data:
                        mix = data['suggested_mix']
                        print(f"✅ {animal_type} - اقتراح الخلطة يعمل")
                        print(f"   المكونات: {len(mix['mix_components'])}")
                        print(f"   التكلفة: {mix['totals']['cost']} د.أ")
                        print(f"   التوصيات: {len(mix['recommendations'])}")
                    else:
                        print(f"❌ {animal_type} - خطأ في البيانات: {data}")
                else:
                    print(f"❌ {animal_type} - خطأ HTTP: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ {animal_type} - خطأ: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 انتهى اختبار اقتراح الخلطة للحيوانات!")

if __name__ == "__main__":
    init_db()
    test_animal_mix_suggestions()