{% extends "base.html" %}

{% block title %}احتياجات البقرة اليومية - برنامج تكوين الأعلاف المركزة{% endblock %}

{% block content %}
<div class="row">
    <!-- قسم إدخال بيانات البقرة -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-cow me-2"></i>
                    بيانات البقرة
                </h5>
            </div>
            <div class="card-body">
                <form id="cowForm">
                    <div class="mb-4">
                        <label for="cowName" class="form-label">
                            <i class="fas fa-tag me-1 text-success"></i>
                            اسم البقرة (اختياري)
                        </label>
                        <input type="text" class="form-control" id="cowName" 
                               placeholder="مثال: بقرة رقم 1" value="{{ cow_name or '' }}">
                        <div class="form-text">يساعد في تمييز السجلات المحفوظة</div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="cowWeight" class="form-label">
                            <i class="fas fa-weight me-1 text-primary"></i>
                            وزن البقرة (كغ) <span class="text-danger">*</span>
                        </label>
                        <input type="number" step="1" min="300" max="800" 
                               class="form-control form-control-lg" id="cowWeight" 
                               placeholder="مثال: 550" required value="{{ weight }}">
                        <div class="form-text">الوزن الطبيعي للأبقار: 400-700 كغ</div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="milkProduction" class="form-label">
                            <i class="fas fa-tint me-1 text-info"></i>
                            إنتاج الحليب اليومي (لتر) <span class="text-danger">*</span>
                        </label>
                        <input type="number" step="0.5" min="0" max="80" 
                               class="form-control form-control-lg" id="milkProduction" 
                               placeholder="مثال: 25" required value="{{ milk }}">
                        <div class="form-text">الإنتاج الطبيعي: 15-40 لتر/يوم</div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="lactationStage" class="form-label">
                            <i class="fas fa-calendar-alt me-1 text-warning"></i>
                            مرحلة الإنتاج
                        </label>
                        <select class="form-select form-select-lg" id="lactationStage">
                            <option value="early" {% if stage == 'early' %}selected{% endif %}>إنتاج مبكر (1-100 يوم)</option>
                            <option value="mid" {% if stage == 'mid' %}selected{% endif %}>إنتاج متوسط (100-200 يوم)</option>
                            <option value="late" {% if stage == 'late' %}selected{% endif %}>إنتاج متأخر (200+ يوم)</option>
                            <option value="dry" {% if stage == 'dry' %}selected{% endif %}>بقرة جافة (غير منتجة)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label for="activityLevel" class="form-label">
                            <i class="fas fa-running me-1 text-secondary"></i>
                            مستوى النشاط
                        </label>
                        <select class="form-select" id="activityLevel">
                            <option value="low" {% if activity == 'low' %}selected{% endif %}>منخفض (حظيرة)</option>
                            <option value="moderate" {% if activity == 'moderate' %}selected{% endif %}>متوسط (رعي محدود)</option>
                            <option value="high" {% if activity == 'high' %}selected{% endif %}>مرتفع (رعي مفتوح)</option>
                        </select>
                    </div>
                    
                    <div class="d-grid">
                        <button type="button" class="btn btn-success btn-lg" id="calculateBtn">
                            <i class="fas fa-calculator me-2"></i>
                            حساب الاحتياجات
                        </button>
                    </div>
                </form>
                
                <!-- معلومات مساعدة -->
                <div class="mt-4">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات مفيدة:
                        </h6>
                        <ul class="mb-0 small">
                            <li>البقرة تحتاج 3-4% من وزنها مادة جافة يومياً</li>
                            <li>كل لتر حليب يحتاج حوالي 0.4-0.5 كغ علف مركز</li>
                            <li>نسبة البروتين المطلوبة: 16-18% للأبقار عالية الإنتاج</li>
                            <li>الطاقة المطلوبة تزيد مع زيادة الإنتاج</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قسم النتائج -->
    <div class="col-lg-8 mb-4">
        <div class="card" id="resultsCard" style="display: none;">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    الاحتياجات اليومية المحسوبة
                </h5>
            </div>
            <div class="card-body">
                <!-- ملخص سريع -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white text-center">
                            <div class="card-body">
                                <i class="fas fa-weight-hanging fa-2x mb-2"></i>
                                <h4 id="totalFeedKg">0</h4>
                                <small>كغ علف/يوم</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white text-center">
                            <div class="card-body">
                                <i class="fas fa-dna fa-2x mb-2"></i>
                                <h4 id="totalProteinKg">0</h4>
                                <small>كغ بروتين/يوم</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white text-center">
                            <div class="card-body">
                                <i class="fas fa-bolt fa-2x mb-2"></i>
                                <h4 id="totalEnergyMcal">0</h4>
                                <small>Mcal طاقة/يوم</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white text-center">
                            <div class="card-body">
                                <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                                <h4 id="dailyCostJD">0</h4>
                                <small>د.أ/يوم</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل مفصلة -->
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-list-ul me-2"></i>
                            التفاصيل المحسوبة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">احتياجات المادة الجافة:</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td>للصيانة:</td>
                                        <td class="fw-bold" id="maintenanceDM">0 كغ</td>
                                    </tr>
                                    <tr>
                                        <td>للإنتاج:</td>
                                        <td class="fw-bold" id="productionDM">0 كغ</td>
                                    </tr>
                                    <tr>
                                        <td>للنشاط:</td>
                                        <td class="fw-bold" id="activityDM">0 كغ</td>
                                    </tr>
                                    <tr class="table-primary">
                                        <td><strong>الإجمالي:</strong></td>
                                        <td class="fw-bold" id="totalDM">0 كغ</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-danger">احتياجات البروتين:</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td>للصيانة:</td>
                                        <td class="fw-bold" id="maintenanceProtein">0 غ</td>
                                    </tr>
                                    <tr>
                                        <td>للإنتاج:</td>
                                        <td class="fw-bold" id="productionProtein">0 غ</td>
                                    </tr>
                                    <tr class="table-danger">
                                        <td><strong>الإجمالي:</strong></td>
                                        <td class="fw-bold" id="totalProtein">0 غ</td>
                                    </tr>
                                    <tr>
                                        <td><strong>النسبة المطلوبة:</strong></td>
                                        <td class="fw-bold" id="proteinPercentage">0%</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <h6 class="text-warning">احتياجات الطاقة:</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td>للصيانة:</td>
                                        <td class="fw-bold" id="maintenanceEnergy">0 Mcal</td>
                                    </tr>
                                    <tr>
                                        <td>للإنتاج:</td>
                                        <td class="fw-bold" id="productionEnergy">0 Mcal</td>
                                    </tr>
                                    <tr>
                                        <td>للنشاط:</td>
                                        <td class="fw-bold" id="activityEnergy">0 Mcal</td>
                                    </tr>
                                    <tr class="table-warning">
                                        <td><strong>الإجمالي:</strong></td>
                                        <td class="fw-bold" id="totalEnergy">0 Mcal</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">احتياجات الألياف:</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td>الحد الأدنى:</td>
                                        <td class="fw-bold" id="minFiber">0 كغ</td>
                                    </tr>
                                    <tr>
                                        <td>الحد الأقصى:</td>
                                        <td class="fw-bold" id="maxFiber">0 كغ</td>
                                    </tr>
                                    <tr class="table-success">
                                        <td><strong>النسبة المثلى:</strong></td>
                                        <td class="fw-bold" id="optimalFiber">18-25%</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- توزيع العلف المقترح -->
                <div class="card mt-4">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            توزيع العلف المقترح
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center p-3 bg-light rounded">
                                    <i class="fas fa-seedling fa-2x text-success mb-2"></i>
                                    <h5 id="concentrateFeed">0 كغ</h5>
                                    <small>علف مركز (60-70%)</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 bg-light rounded">
                                    <i class="fas fa-leaf fa-2x text-primary mb-2"></i>
                                    <h5 id="roughageFeed">0 كغ</h5>
                                    <small>علف خشن (25-35%)</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 bg-light rounded">
                                    <i class="fas fa-plus fa-2x text-warning mb-2"></i>
                                    <h5 id="supplementFeed">0 كغ</h5>
                                    <small>إضافات (5-10%)</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تكلفة مفصلة -->
                <div class="card mt-4">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-calculator me-2"></i>
                            تحليل التكلفة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-sm">
                                    <tr>
                                        <td>التكلفة اليومية:</td>
                                        <td class="fw-bold text-success" id="dailyCost">0 د.أ</td>
                                    </tr>
                                    <tr>
                                        <td>التكلفة الشهرية:</td>
                                        <td class="fw-bold text-info" id="monthlyCost">0 د.أ</td>
                                    </tr>
                                    <tr>
                                        <td>التكلفة السنوية:</td>
                                        <td class="fw-bold text-warning" id="yearlyCost">0 د.أ</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-sm">
                                    <tr>
                                        <td>تكلفة/لتر حليب:</td>
                                        <td class="fw-bold text-primary" id="costPerLiter">0 د.أ</td>
                                    </tr>
                                    <tr>
                                        <td>تكلفة/كغ علف:</td>
                                        <td class="fw-bold text-secondary" id="costPerKg">0 د.أ</td>
                                    </tr>
                                    <tr>
                                        <td>كفاءة التحويل:</td>
                                        <td class="fw-bold text-danger" id="feedEfficiency">0</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار العمليات -->
                <div class="row mt-4">
                    <div class="col-md-4">
                        <button type="button" class="btn btn-outline-primary w-100" id="suggestMixBtn" disabled>
                            <i class="fas fa-lightbulb me-2"></i>
                            اقتراح خلطة مناسبة
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button type="button" class="btn btn-outline-success w-100" id="saveRequirementsBtn" disabled>
                            <i class="fas fa-save me-2"></i>
                            حفظ الحسابات
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button type="button" class="btn btn-outline-info w-100" id="printBtn">
                            <i class="fas fa-print me-2"></i>
                            طباعة التقرير
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم الخلطة المقترحة -->
        <div class="card mt-4" id="suggestedMixCard" style="display: none;">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-magic me-2"></i>
                    الخلطة المقترحة
                </h5>
            </div>
            <div class="card-body">
                <!-- ملخص الخلطة -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title text-primary">مواصفات الخلطة:</h6>
                                <table class="table table-sm table-borderless">
                                    <tr>
                                        <td>إجمالي الوزن:</td>
                                        <td class="fw-bold" id="mixTotalWeight">0 كغ</td>
                                    </tr>
                                    <tr>
                                        <td>التكلفة الإجمالية:</td>
                                        <td class="fw-bold" id="mixTotalCost">0 د.أ</td>
                                    </tr>
                                    <tr>
                                        <td>التكلفة/كغ:</td>
                                        <td class="fw-bold" id="mixCostPerKg">0 د.أ</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title text-success">مؤشرات الجودة:</h6>
                                <table class="table table-sm table-borderless">
                                    <tr>
                                        <td>دقة البروتين:</td>
                                        <td class="fw-bold" id="proteinMatch">0%</td>
                                    </tr>
                                    <tr>
                                        <td>دقة الطاقة:</td>
                                        <td class="fw-bold" id="energyMatch">0%</td>
                                    </tr>
                                    <tr>
                                        <td>كفاءة التكلفة:</td>
                                        <td class="fw-bold" id="costEfficiency">0</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- مكونات الخلطة -->
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0">مكونات الخلطة المقترحة</h6>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-dark">
                                    <tr>
                                        <th>المكون</th>
                                        <th class="text-center">الكمية (كغ)</th>
                                        <th class="text-center">النسبة %</th>
                                        <th class="text-center">التكلفة (د.أ)</th>
                                        <th>السبب</th>
                                    </tr>
                                </thead>
                                <tbody id="mixComponentsTable">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- التوصيات -->
                <div class="card mt-3" id="recommendationsCard">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-clipboard-list me-2"></i>
                            التوصيات والملاحظات
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="recommendationsList">
                        </div>
                    </div>
                </div>

                <!-- أزرار الخلطة -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <button type="button" class="btn btn-success w-100" id="acceptMixBtn">
                            <i class="fas fa-check me-2"></i>
                            قبول الخلطة وحفظها
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button type="button" class="btn btn-primary w-100" id="modifyMixBtn">
                            <i class="fas fa-edit me-2"></i>
                            تعديل الخلطة
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- رسالة عدم وجود نتائج -->
        <div class="card" id="noResultsCard">
            <div class="card-body text-center py-5">
                <i class="fas fa-cow fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">أدخل بيانات البقرة لحساب احتياجاتها</h4>
                <p class="text-muted">املأ النموذج على اليسار واضغط "حساب الاحتياجات"</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

<!-- نافذة حفظ الحسابات -->
<div class="modal fade" id="saveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">حفظ حسابات البقرة</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="cowNameInput" class="form-label">اسم البقرة أو الرقم:</label>
                    <input type="text" class="form-control" id="cowNameInput" 
                           placeholder="مثال: بقرة رقم 101 أو فاطمة" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">ملخص البيانات:</label>
                    <div class="bg-light p-3 rounded">
                        <p class="mb-1"><strong>الوزن:</strong> <span id="modalWeight"></span> كغ</p>
                        <p class="mb-1"><strong>إنتاج الحليب:</strong> <span id="modalMilk"></span> لتر/يوم</p>
                        <p class="mb-1"><strong>العلف المطلوب:</strong> <span id="modalFeed"></span> كغ/يوم</p>
                        <p class="mb-0"><strong>التكلفة:</strong> <span id="modalCost"></span> د.أ/يوم</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" id="confirmSaveBtn">حفظ البيانات</button>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
let currentRequirements = null;
let currentSuggestedMix = null;
let saveModal = null;

document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculateBtn');
    const suggestMixBtn = document.getElementById('suggestMixBtn');
    const saveRequirementsBtn = document.getElementById('saveRequirementsBtn');
    const printBtn = document.getElementById('printBtn');
    const acceptMixBtn = document.getElementById('acceptMixBtn');
    const modifyMixBtn = document.getElementById('modifyMixBtn');
    const confirmSaveBtn = document.getElementById('confirmSaveBtn');
    
    saveModal = new bootstrap.Modal(document.getElementById('saveModal'));
    
    calculateBtn.addEventListener('click', calculateRequirements);
    suggestMixBtn.addEventListener('click', suggestMix);
    saveRequirementsBtn.addEventListener('click', showSaveModal);
    printBtn.addEventListener('click', printReport);
    acceptMixBtn.addEventListener('click', acceptMix);
    modifyMixBtn.addEventListener('click', modifyMix);
    confirmSaveBtn.addEventListener('click', saveRequirements);
});

function calculateRequirements() {
    console.log('🐄 بدء حساب احتياجات البقرة...');
    
    const cowWeight = parseFloat(document.getElementById('cowWeight').value);
    const milkProduction = parseFloat(document.getElementById('milkProduction').value);
    const lactationStage = document.getElementById('lactationStage').value;
    const activityLevel = document.getElementById('activityLevel').value;
    
    console.log('البيانات المدخلة:', { cowWeight, milkProduction, lactationStage, activityLevel });
    
    if (!cowWeight || cowWeight < 300 || cowWeight > 800) {
        alert('يرجى إدخال وزن صحيح للبقرة (300-800 كغ)');
        return;
    }
    
    if (milkProduction < 0 || milkProduction > 80) {
        alert('يرجى إدخال إنتاج حليب صحيح (0-80 لتر)');
        return;
    }
    
    // حساب الاحتياجات باستخدام معادلات NRC
    console.log('🧮 بدء الحسابات...');
    const requirements = calculateNutritionalRequirements(cowWeight, milkProduction, lactationStage, activityLevel);
    console.log('📊 نتائج الحساب:', requirements);
    
    // حفظ النتائج الحالية
    currentRequirements = requirements;
    
    // عرض النتائج
    console.log('📱 عرض النتائج...');
    displayRequirements(requirements);
    console.log('✅ تم عرض النتائج بنجاح!');
    
    // تفعيل الأزرار
    document.getElementById('suggestMixBtn').disabled = false;
    document.getElementById('saveRequirementsBtn').disabled = false;
    
    // إظهار قسم النتائج
    document.getElementById('resultsCard').style.display = 'block';
    document.getElementById('noResultsCard').style.display = 'none';
    document.getElementById('resultsCard').scrollIntoView({ behavior: 'smooth' });
}

function calculateNutritionalRequirements(weight, milk, stage, activity) {
    // معاملات التصحيح
    const stageFactors = {
        'early': { dm: 1.1, protein: 1.15, energy: 1.1 },
        'mid': { dm: 1.0, protein: 1.0, energy: 1.0 },
        'late': { dm: 0.95, protein: 0.9, energy: 0.95 },
        'dry': { dm: 0.8, protein: 0.7, energy: 0.8 }
    };
    
    const activityFactors = {
        'low': { dm: 1.0, energy: 1.0 },
        'moderate': { dm: 1.05, energy: 1.1 },
        'high': { dm: 1.1, energy: 1.2 }
    };
    
    const stageFactor = stageFactors[stage];
    const activityFactor = activityFactors[activity];
    
    // حساب المادة الجافة (DM)
    const maintenanceDM = weight * 0.026; // 2.6% من وزن الجسم للصيانة
    const productionDM = milk * 0.37; // 0.37 كغ لكل لتر حليب
    const activityDM = maintenanceDM * (activityFactor.dm - 1);
    const totalDM = (maintenanceDM + productionDM + activityDM) * stageFactor.dm;
    
    // حساب الطاقة (NEL - Net Energy for Lactation)
    const maintenanceEnergy = Math.pow(weight, 0.75) * 0.08; // Mcal
    const productionEnergy = milk * 0.74; // Mcal لكل لتر حليب (3.5% دهن)
    const activityEnergy = maintenanceEnergy * (activityFactor.energy - 1);
    const totalEnergy = (maintenanceEnergy + productionEnergy + activityEnergy) * stageFactor.energy;
    
    // حساب البروتين (MP - Metabolizable Protein)
    const maintenanceProtein = Math.pow(weight, 0.75) * 3.8; // غرام
    const productionProtein = milk * 58; // غرام لكل لتر حليب (3.2% بروتين)
    const totalProtein = (maintenanceProtein + productionProtein) * stageFactor.protein;
    const proteinPercentage = (totalProtein / 1000) / totalDM * 100;
    
    // حساب الألياف
    const minFiber = totalDM * 0.15; // 15% كحد أدنى
    const maxFiber = totalDM * 0.30; // 30% كحد أقصى
    
    // توزيع العلف المقترح
    const concentrateFeed = totalDM * 0.65; // 65% علف مركز
    const roughageFeed = totalDM * 0.30; // 30% علف خشن
    const supplementFeed = totalDM * 0.05; // 5% إضافات
    
    // حساب التكلفة (متوسط سعر العلف 0.4 د.أ/كغ)
    const avgFeedCost = 0.4;
    const dailyCost = totalDM * avgFeedCost;
    const monthlyCost = dailyCost * 30;
    const yearlyCost = dailyCost * 365;
    const costPerLiter = milk > 0 ? dailyCost / milk : 0;
    const costPerKg = avgFeedCost;
    const feedEfficiency = milk > 0 ? totalDM / milk : 0;
    
    return {
        // مادة جافة
        maintenanceDM: maintenanceDM,
        productionDM: productionDM,
        activityDM: activityDM,
        totalDM: totalDM,
        
        // طاقة
        maintenanceEnergy: maintenanceEnergy,
        productionEnergy: productionEnergy,
        activityEnergy: activityEnergy,
        totalEnergy: totalEnergy,
        
        // بروتين
        maintenanceProtein: maintenanceProtein,
        productionProtein: productionProtein,
        totalProtein: totalProtein,
        proteinPercentage: proteinPercentage,
        
        // ألياف
        minFiber: minFiber,
        maxFiber: maxFiber,
        
        // توزيع العلف
        concentrateFeed: concentrateFeed,
        roughageFeed: roughageFeed,
        supplementFeed: supplementFeed,
        
        // تكلفة
        dailyCost: dailyCost,
        monthlyCost: monthlyCost,
        yearlyCost: yearlyCost,
        costPerLiter: costPerLiter,
        costPerKg: costPerKg,
        feedEfficiency: feedEfficiency
    };
}

function displayRequirements(req) {
    // الملخص السريع
    document.getElementById('totalFeedKg').textContent = req.totalDM.toFixed(1);
    document.getElementById('totalProteinKg').textContent = (req.totalProtein / 1000).toFixed(2);
    document.getElementById('totalEnergyMcal').textContent = req.totalEnergy.toFixed(1);
    document.getElementById('dailyCostJD').textContent = req.dailyCost.toFixed(2);
    
    // تفاصيل المادة الجافة
    document.getElementById('maintenanceDM').textContent = req.maintenanceDM.toFixed(1) + ' كغ';
    document.getElementById('productionDM').textContent = req.productionDM.toFixed(1) + ' كغ';
    document.getElementById('activityDM').textContent = req.activityDM.toFixed(1) + ' كغ';
    document.getElementById('totalDM').textContent = req.totalDM.toFixed(1) + ' كغ';
    
    // تفاصيل البروتين
    document.getElementById('maintenanceProtein').textContent = req.maintenanceProtein.toFixed(0) + ' غ';
    document.getElementById('productionProtein').textContent = req.productionProtein.toFixed(0) + ' غ';
    document.getElementById('totalProtein').textContent = req.totalProtein.toFixed(0) + ' غ';
    document.getElementById('proteinPercentage').textContent = req.proteinPercentage.toFixed(1) + '%';
    
    // تفاصيل الطاقة
    document.getElementById('maintenanceEnergy').textContent = req.maintenanceEnergy.toFixed(1) + ' Mcal';
    document.getElementById('productionEnergy').textContent = req.productionEnergy.toFixed(1) + ' Mcal';
    document.getElementById('activityEnergy').textContent = req.activityEnergy.toFixed(1) + ' Mcal';
    document.getElementById('totalEnergy').textContent = req.totalEnergy.toFixed(1) + ' Mcal';
    
    // تفاصيل الألياف
    document.getElementById('minFiber').textContent = req.minFiber.toFixed(1) + ' كغ';
    document.getElementById('maxFiber').textContent = req.maxFiber.toFixed(1) + ' كغ';
    
    // توزيع العلف
    document.getElementById('concentrateFeed').textContent = req.concentrateFeed.toFixed(1) + ' كغ';
    document.getElementById('roughageFeed').textContent = req.roughageFeed.toFixed(1) + ' كغ';
    document.getElementById('supplementFeed').textContent = req.supplementFeed.toFixed(1) + ' كغ';
    
    // تفاصيل التكلفة
    document.getElementById('dailyCost').textContent = req.dailyCost.toFixed(2) + ' د.أ';
    document.getElementById('monthlyCost').textContent = req.monthlyCost.toFixed(2) + ' د.أ';
    document.getElementById('yearlyCost').textContent = req.yearlyCost.toFixed(2) + ' د.أ';
    document.getElementById('costPerLiter').textContent = req.costPerLiter.toFixed(3) + ' د.أ';
    document.getElementById('costPerKg').textContent = req.costPerKg.toFixed(3) + ' د.أ';
    document.getElementById('feedEfficiency').textContent = req.feedEfficiency.toFixed(2);
}

function suggestMix() {
    if (!currentRequirements) {
        alert('يرجى حساب الاحتياجات أولاً');
        return;
    }
    
    fetch('/suggest_mix_for_cow', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            requirements: currentRequirements
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('خطأ: ' + data.error);
            return;
        }
        
        currentSuggestedMix = data.suggested_mix;
        displaySuggestedMix(data.suggested_mix);
        document.getElementById('suggestedMixCard').style.display = 'block';
        document.getElementById('suggestedMixCard').scrollIntoView({ behavior: 'smooth' });
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في اقتراح الخلطة');
    });
}

function displaySuggestedMix(suggestedMix) {
    // عرض ملخص الخلطة
    document.getElementById('mixTotalWeight').textContent = suggestedMix.totals.weight + ' كغ';
    document.getElementById('mixTotalCost').textContent = suggestedMix.totals.cost + ' د.أ';
    document.getElementById('mixCostPerKg').textContent = suggestedMix.totals.cost_per_kg + ' د.أ';
    
    // عرض مؤشرات الجودة
    document.getElementById('proteinMatch').textContent = suggestedMix.quality_indicators.protein_match + '%';
    document.getElementById('energyMatch').textContent = suggestedMix.quality_indicators.energy_match + '%';
    document.getElementById('costEfficiency').textContent = suggestedMix.quality_indicators.cost_efficiency;
    
    // عرض مكونات الخلطة
    const tableBody = document.getElementById('mixComponentsTable');
    tableBody.innerHTML = '';
    
    suggestedMix.mix_components.forEach(component => {
        const row = document.createElement('tr');
        const cost = component.quantity * component.ingredient.price_per_kg;
        row.innerHTML = `
            <td>
                <strong>${component.ingredient.name}</strong>
                <br><small class="text-muted">${component.ingredient.protein}% بروتين، ${component.ingredient.energy} kcal/kg</small>
            </td>
            <td class="text-center">${component.quantity.toFixed(2)}</td>
            <td class="text-center">${component.percentage.toFixed(1)}%</td>
            <td class="text-center">${cost.toFixed(3)}</td>
            <td><small class="text-info">${component.reason}</small></td>
        `;
        tableBody.appendChild(row);
    });
    
    // عرض التوصيات
    const recommendationsList = document.getElementById('recommendationsList');
    recommendationsList.innerHTML = '';
    
    suggestedMix.recommendations.forEach(rec => {
        const alertClass = rec.type === 'good' ? 'alert-success' : 
                          rec.type.includes('low') ? 'alert-warning' : 'alert-info';
        
        const recDiv = document.createElement('div');
        recDiv.className = `alert ${alertClass} mb-2`;
        recDiv.innerHTML = `
            <strong>${rec.message}</strong><br>
            <small>${rec.suggestion}</small>
        `;
        recommendationsList.appendChild(recDiv);
    });
}

function showSaveModal() {
    if (!currentRequirements) {
        alert('يرجى حساب الاحتياجات أولاً');
        return;
    }
    
    // ملء البيانات في النافذة
    document.getElementById('modalWeight').textContent = document.getElementById('cowWeight').value;
    document.getElementById('modalMilk').textContent = document.getElementById('milkProduction').value;
    document.getElementById('modalFeed').textContent = currentRequirements.total_dm;
    document.getElementById('modalCost').textContent = currentRequirements.daily_cost;
    
    saveModal.show();
}

function saveRequirements() {
    let cowName = document.getElementById('cowNameInput').value.trim();
    if (!cowName) {
        // استخدم اسم البقرة من النموذج الرئيسي إذا لم يتم إدخاله في النافذة
        cowName = document.getElementById('cowName').value.trim();
        if (!cowName) {
            cowName = 'بقرة بدون اسم - ' + new Date().toLocaleDateString();
        }
    }
    
    const saveData = {
        cow_name: cowName,
        weight: parseFloat(document.getElementById('cowWeight').value),
        milk_production: parseFloat(document.getElementById('milkProduction').value),
        lactation_stage: document.getElementById('lactationStage').value,
        activity_level: document.getElementById('activityLevel').value,
        requirements: currentRequirements
    };
    
    fetch('/save_cow_calculation', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(saveData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('خطأ: ' + data.error);
            return;
        }
        
        alert(data.message);
        saveModal.hide();
        document.getElementById('cowNameInput').value = '';
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في حفظ البيانات');
    });
}

function acceptMix() {
    if (!currentSuggestedMix) {
        alert('لا توجد خلطة مقترحة للقبول');
        return;
    }
    
    // تحويل المستخدم لصفحة تكوين الخلطات مع البيانات المقترحة
    const mixData = {
        name: 'خلطة مقترحة للبقرة',
        components: currentSuggestedMix.mix_components
    };
    
    // حفظ البيانات في Local Storage للانتقال
    localStorage.setItem('suggestedMixData', JSON.stringify(mixData));
    
    // الانتقال للصفحة الرئيسية
    window.location.href = '/';
}

function modifyMix() {
    if (!currentSuggestedMix) {
        alert('لا توجد خلطة مقترحة للتعديل');
        return;
    }
    
    // تحويل المستخدم لصفحة تكوين الخلطات مع إمكانية التعديل
    acceptMix();
}

function printReport() {
    window.print();
}
</script>
{% endblock %}