{% extends "base.html" %}

{% block title %}احتياجات البقرة اليومية - برنامج تكوين الأعلاف المركزة{% endblock %}

{% block content %}
<div class="row">
    <!-- قسم إدخال بيانات البقرة -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-cow me-2"></i>
                    بيانات البقرة
                </h5>
            </div>
            <div class="card-body">
                <form id="cowForm">
                    <div class="mb-4">
                        <label for="cowName" class="form-label">
                            <i class="fas fa-tag me-1 text-success"></i>
                            اسم البقرة (اختياري)
                        </label>
                        <input type="text" class="form-control" id="cowName" 
                               placeholder="مثال: بقرة رقم 1" value="{{ cow_name or '' }}">
                        <div class="form-text">يساعد في تمييز السجلات المحفوظة</div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="cowWeight" class="form-label">
                            <i class="fas fa-weight me-1 text-primary"></i>
                            وزن البقرة (كغ) <span class="text-danger">*</span>
                        </label>
                        <input type="number" step="1" min="300" max="800" 
                               class="form-control form-control-lg" id="cowWeight" 
                               placeholder="مثال: 550" required value="{{ weight }}">
                        <div class="form-text">الوزن الطبيعي للأبقار: 400-700 كغ</div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="milkProduction" class="form-label">
                            <i class="fas fa-tint me-1 text-info"></i>
                            إنتاج الحليب اليومي (لتر) <span class="text-danger">*</span>
                        </label>
                        <input type="number" step="0.5" min="0" max="80" 
                               class="form-control form-control-lg" id="milkProduction" 
                               placeholder="مثال: 25" required value="{{ milk }}">
                        <div class="form-text">الإنتاج الطبيعي: 15-40 لتر/يوم</div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="lactationStage" class="form-label">
                            <i class="fas fa-calendar-alt me-1 text-warning"></i>
                            مرحلة الإنتاج
                        </label>
                        <select class="form-select form-select-lg" id="lactationStage">
                            <option value="early" {% if stage == 'early' %}selected{% endif %}>إنتاج مبكر (1-100 يوم)</option>
                            <option value="mid" {% if stage == 'mid' %}selected{% endif %}>إنتاج متوسط (100-200 يوم)</option>
                            <option value="late" {% if stage == 'late' %}selected{% endif %}>إنتاج متأخر (200+ يوم)</option>
                            <option value="dry" {% if stage == 'dry' %}selected{% endif %}>بقرة جافة (غير منتجة)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label for="activityLevel" class="form-label">
                            <i class="fas fa-running me-1 text-secondary"></i>
                            مستوى النشاط
                        </label>
                        <select class="form-select" id="activityLevel">
                            <option value="low" {% if activity == 'low' %}selected{% endif %}>منخفض (حظيرة)</option>
                            <option value="moderate" {% if activity == 'moderate' %}selected{% endif %}>متوسط (رعي محدود)</option>
                            <option value="high" {% if activity == 'high' %}selected{% endif %}>مرتفع (رعي مفتوح)</option>
                        </select>
                    </div>
                    
                    <div class="d-grid">
                        <button type="button" class="btn btn-success btn-lg" id="calculateBtn">
                            <i class="fas fa-calculator me-2"></i>
                            حساب الاحتياجات
                        </button>
                    </div>
                </form>
                
                <!-- معلومات مساعدة -->
                <div class="mt-4">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات مفيدة:
                        </h6>
                        <ul class="mb-0 small">
                            <li>البقرة تحتاج 3-4% من وزنها مادة جافة يومياً</li>
                            <li>كل لتر حليب يحتاج حوالي 0.4-0.5 كغ علف مركز</li>
                            <li>نسبة البروتين المطلوبة: 16-18% للأبقار عالية الإنتاج</li>
                            <li>الطاقة المطلوبة تزيد مع زيادة الإنتاج</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قسم النتائج -->
    <div class="col-lg-8 mb-4">
        <div class="card" id="resultsCard" style="display: none;">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    الاحتياجات اليومية المحسوبة
                </h5>
            </div>
            <div class="card-body">
                <!-- ملخص سريع -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white text-center">
                            <div class="card-body">
                                <i class="fas fa-weight-hanging fa-2x mb-2"></i>
                                <h4 id="totalFeedKg">0</h4>
                                <small>كغ علف/يوم</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white text-center">
                            <div class="card-body">
                                <i class="fas fa-dna fa-2x mb-2"></i>
                                <h4 id="totalProteinKg">0</h4>
                                <small>كغ بروتين/يوم</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white text-center">
                            <div class="card-body">
                                <i class="fas fa-bolt fa-2x mb-2"></i>
                                <h4 id="totalEnergyMcal">0</h4>
                                <small>Mcal طاقة/يوم</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white text-center">
                            <div class="card-body">
                                <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                                <h4 id="dailyCostJD">0</h4>
                                <small>د.أ/يوم</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل مفصلة -->
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-list-ul me-2"></i>
                            التفاصيل المحسوبة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">احتياجات المادة الجافة:</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td>للصيانة:</td>
                                        <td class="fw-bold" id="maintenanceDM">0 كغ</td>
                                    </tr>
                                    <tr>
                                        <td>للإنتاج:</td>
                                        <td class="fw-bold" id="productionDM">0 كغ</td>
                                    </tr>
                                    <tr>
                                        <td>للنشاط:</td>
                                        <td class="fw-bold" id="activityDM">0 كغ</td>
                                    </tr>
                                    <tr class="table-primary">
                                        <td><strong>الإجمالي:</strong></td>
                                        <td class="fw-bold" id="totalDM">0 كغ</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-danger">احتياجات البروتين:</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td>للصيانة:</td>
                                        <td class="fw-bold" id="maintenanceProtein">0 غ</td>
                                    </tr>
                                    <tr>
                                        <td>للإنتاج:</td>
                                        <td class="fw-bold" id="productionProtein">0 غ</td>
                                    </tr>
                                    <tr class="table-danger">
                                        <td><strong>الإجمالي:</strong></td>
                                        <td class="fw-bold" id="totalProtein">0 غ</td>
                                    </tr>
                                    <tr>
                                        <td><strong>النسبة المطلوبة:</strong></td>
                                        <td class="fw-bold" id="proteinPercentage">0%</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <h6 class="text-warning">احتياجات الطاقة:</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td>للصيانة:</td>
                                        <td class="fw-bold" id="maintenanceEnergy">0 Mcal</td>
                                    </tr>
                                    <tr>
                                        <td>للإنتاج:</td>
                                        <td class="fw-bold" id="productionEnergy">0 Mcal</td>
                                    </tr>
                                    <tr>
                                        <td>للنشاط:</td>
                                        <td class="fw-bold" id="activityEnergy">0 Mcal</td>
                                    </tr>
                                    <tr class="table-warning">
                                        <td><strong>الإجمالي:</strong></td>
                                        <td class="fw-bold" id="totalEnergy">0 Mcal</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">احتياجات الألياف:</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td>الحد الأدنى:</td>
                                        <td class="fw-bold" id="minFiber">0 كغ</td>
                                    </tr>
                                    <tr>
                                        <td>الحد الأقصى:</td>
                                        <td class="fw-bold" id="maxFiber">0 كغ</td>
                                    </tr>
                                    <tr class="table-success">
                                        <td><strong>النسبة المثلى:</strong></td>
                                        <td class="fw-bold" id="optimalFiber">18-25%</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- توزيع العلف المقترح -->
                <div class="card mt-4">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            توزيع العلف المقترح
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center p-3 bg-light rounded">
                                    <i class="fas fa-seedling fa-2x text-success mb-2"></i>
                                    <h5 id="concentrateFeed">0 كغ</h5>
                                    <small>علف مركز (60-70%)</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 bg-light rounded">
                                    <i class="fas fa-leaf fa-2x text-primary mb-2"></i>
                                    <h5 id="roughageFeed">0 كغ</h5>
                                    <small>علف خشن (25-35%)</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 bg-light rounded">
                                    <i class="fas fa-plus fa-2x text-warning mb-2"></i>
                                    <h5 id="supplementFeed">0 كغ</h5>
                                    <small>إضافات (5-10%)</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تكلفة مفصلة -->
                <div class="card mt-4">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-calculator me-2"></i>
                            تحليل التكلفة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-sm">
                                    <tr>
                                        <td>التكلفة اليومية:</td>
                                        <td class="fw-bold text-success" id="dailyCost">0 د.أ</td>
                                    </tr>
                                    <tr>
                                        <td>التكلفة الشهرية:</td>
                                        <td class="fw-bold text-info" id="monthlyCost">0 د.أ</td>
                                    </tr>
                                    <tr>
                                        <td>التكلفة السنوية:</td>
                                        <td class="fw-bold text-warning" id="yearlyCost">0 د.أ</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-sm">
                                    <tr>
                                        <td>تكلفة/لتر حليب:</td>
                                        <td class="fw-bold text-primary" id="costPerLiter">0 د.أ</td>
                                    </tr>
                                    <tr>
                                        <td>تكلفة/كغ علف:</td>
                                        <td class="fw-bold text-secondary" id="costPerKg">0 د.أ</td>
                                    </tr>
                                    <tr>
                                        <td>كفاءة التحويل:</td>
                                        <td class="fw-bold text-danger" id="feedEfficiency">0</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار العمليات -->
                <div class="row mt-4">
                    <div class="col-md-4">
                        <button type="button" class="btn btn-outline-primary w-100" id="suggestMixBtn" disabled>
                            <i class="fas fa-lightbulb me-2"></i>
                            اقتراح خلطة مناسبة
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button type="button" class="btn btn-outline-success w-100" id="saveRequirementsBtn" disabled>
                            <i class="fas fa-save me-2"></i>
                            حفظ الحسابات
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button type="button" class="btn btn-outline-info w-100" id="printBtn">
                            <i class="fas fa-print me-2"></i>
                            طباعة التقرير
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم الخلطة المقترحة -->
        <div class="card mt-4" id="suggestedMixCard" style="display: none;">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-magic me-2"></i>
                    الخلطة المقترحة
                </h5>
            </div>
            <div class="card-body">
                <!-- ملخص الخلطة -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title text-primary">مواصفات الخلطة:</h6>
                                <table class="table table-sm table-borderless">
                                    <tr>
                                        <td>إجمالي الوزن:</td>
                                        <td class="fw-bold" id="mixTotalWeight">0 كغ</td>
                                    </tr>
                                    <tr>
                                        <td>التكلفة الإجمالية:</td>
                                        <td class="fw-bold" id="mixTotalCost">0 د.أ</td>
                                    </tr>
                                    <tr>
                                        <td>التكلفة/كغ:</td>
                                        <td class="fw-bold" id="mixCostPerKg">0 د.أ</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title text-success">مؤشرات الجودة:</h6>
                                <table class="table table-sm table-borderless">
                                    <tr>
                                        <td>دقة البروتين:</td>
                                        <td class="fw-bold" id="proteinMatch">0%</td>
                                    </tr>
                                    <tr>
                                        <td>دقة الطاقة:</td>
                                        <td class="fw-bold" id="energyMatch">0%</td>
                                    </tr>
                                    <tr>
                                        <td>كفاءة التكلفة:</td>
                                        <td class="fw-bold" id="costEfficiency">0</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- مكونات الخلطة -->
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0">مكونات الخلطة المقترحة</h6>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-dark">
                                    <tr>
                                        <th>المكون</th>
                                        <th class="text-center">الكمية (كغ)</th>
                                        <th class="text-center">النسبة %</th>
                                        <th class="text-center">التكلفة (د.أ)</th>
                                        <th>السبب</th>
                                    </tr>
                                </thead>
                                <tbody id="mixComponentsTable">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- التوصيات -->
                <div class="card mt-3" id="recommendationsCard">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-clipboard-list me-2"></i>
                            التوصيات والملاحظات
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="recommendationsList">
                        </div>
                    </div>
                </div>

                <!-- أزرار الخلطة -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <button type="button" class="btn btn-success w-100" id="acceptMixBtn">
                            <i class="fas fa-check me-2"></i>
                            قبول الخلطة وحفظها
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button type="button" class="btn btn-primary w-100" id="modifyMixBtn">
                            <i class="fas fa-edit me-2"></i>
                            تعديل الخلطة
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- رسالة عدم وجود نتائج -->
        <div class="card" id="noResultsCard">
            <div class="card-body text-center py-5">
                <i class="fas fa-cow fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">أدخل بيانات البقرة لحساب احتياجاتها</h4>
                <p class="text-muted">املأ النموذج على اليسار واضغط "حساب الاحتياجات"</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

<!-- نافذة حفظ الحسابات -->
<div class="modal fade" id="saveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">حفظ حسابات البقرة</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="cowNameInput" class="form-label">اسم البقرة أو الرقم:</label>
                    <input type="text" class="form-control" id="cowNameInput" 
                           placeholder="مثال: بقرة رقم 101 أو فاطمة" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">ملخص البيانات:</label>
                    <div class="bg-light p-3 rounded">
                        <p class="mb-1"><strong>الوزن:</strong> <span id="modalWeight"></span> كغ</p>
                        <p class="mb-1"><strong>إنتاج الحليب:</strong> <span id="modalMilk"></span> لتر/يوم</p>
                        <p class="mb-1"><strong>العلف المطلوب:</strong> <span id="modalFeed"></span> كغ/يوم</p>
                        <p class="mb-0"><strong>التكلفة:</strong> <span id="modalCost"></span> د.أ/يوم</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" id="confirmSaveBtn">حفظ البيانات</button>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
let currentRequirements = null;
let currentSuggestedMix = null;
let saveModal = null;

document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 بدء تحميل JavaScript...');

    const calculateBtn = document.getElementById('calculateBtn');
    const suggestMixBtn = document.getElementById('suggestMixBtn');
    const saveRequirementsBtn = document.getElementById('saveRequirementsBtn');
    const printBtn = document.getElementById('printBtn');
    const acceptMixBtn = document.getElementById('acceptMixBtn');
    const modifyMixBtn = document.getElementById('modifyMixBtn');
    const confirmSaveBtn = document.getElementById('confirmSaveBtn');

    console.log('🔍 فحص العناصر:');
    console.log('calculateBtn:', calculateBtn);
    console.log('suggestMixBtn:', suggestMixBtn);
    console.log('saveRequirementsBtn:', saveRequirementsBtn);
    console.log('printBtn:', printBtn);
    console.log('acceptMixBtn:', acceptMixBtn);
    console.log('modifyMixBtn:', modifyMixBtn);
    console.log('confirmSaveBtn:', confirmSaveBtn);

    if (!calculateBtn) {
        console.error('❌ calculateBtn غير موجود!');
        alert('خطأ: زر حساب الاحتياجات غير موجود!');
        return;
    }

    try {
        saveModal = new bootstrap.Modal(document.getElementById('saveModal'));
        console.log('✅ تم إنشاء saveModal');
    } catch (error) {
        console.error('❌ خطأ في إنشاء saveModal:', error);
    }

    try {
        calculateBtn.addEventListener('click', calculateRequirements);
        console.log('✅ تم ربط calculateBtn');

        suggestMixBtn.addEventListener('click', suggestMix);
        console.log('✅ تم ربط suggestMixBtn');

        saveRequirementsBtn.addEventListener('click', showSaveModal);
        console.log('✅ تم ربط saveRequirementsBtn');

        printBtn.addEventListener('click', printReport);
        console.log('✅ تم ربط printBtn');

        acceptMixBtn.addEventListener('click', acceptMix);
        console.log('✅ تم ربط acceptMixBtn');

        modifyMixBtn.addEventListener('click', modifyMix);
        console.log('✅ تم ربط modifyMixBtn');

        confirmSaveBtn.addEventListener('click', saveRequirements);
        console.log('✅ تم ربط confirmSaveBtn');

        console.log('🎉 تم ربط جميع الأحداث بنجاح!');
    } catch (error) {
        console.error('❌ خطأ في ربط الأحداث:', error);
        alert('خطأ في ربط الأحداث: ' + error.message);
    }
});

function calculateRequirements() {
    console.log('🐄 بدء حساب احتياجات البقرة...');
    alert('تم استدعاء دالة حساب الاحتياجات!');

    const cowWeight = parseFloat(document.getElementById('cowWeight').value);
    const milkProduction = parseFloat(document.getElementById('milkProduction').value);
    const lactationStage = document.getElementById('lactationStage').value;
    const activityLevel = document.getElementById('activityLevel').value;

    console.log('البيانات المدخلة:', { cowWeight, milkProduction, lactationStage, activityLevel });

    if (!cowWeight || cowWeight < 300 || cowWeight > 800) {
        alert('يرجى إدخال وزن صحيح للبقرة (300-800 كغ)');
        return;
    }

    if (milkProduction < 0 || milkProduction > 80) {
        alert('يرجى إدخال إنتاج حليب صحيح (0-80 لتر)');
        return;
    }

    // إظهار مؤشر التحميل
    const calculateBtn = document.getElementById('calculateBtn');
    const originalText = calculateBtn.innerHTML;
    calculateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحساب...';
    calculateBtn.disabled = true;

    // إرسال البيانات إلى API
    console.log('🌐 إرسال البيانات إلى الخادم...');

    const requestData = {
        animal_type: 'cow',
        cow_weight: cowWeight,
        cow_milk: milkProduction,
        cow_stage: lactationStage,
        activity_level: activityLevel
    };

    fetch('/calculate_animal_requirements', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        console.log('📊 استجابة الخادم:', data);

        if (data.error) {
            alert('خطأ: ' + data.error);
            return;
        }

        if (data.requirements) {
            // حفظ النتائج الحالية
            currentRequirements = data.requirements;

            // عرض النتائج
            console.log('📱 عرض النتائج...');
            displayRequirements(data.requirements);
            console.log('✅ تم عرض النتائج بنجاح!');

            // تفعيل الأزرار
            document.getElementById('suggestMixBtn').disabled = false;
            document.getElementById('saveRequirementsBtn').disabled = false;

            // إظهار قسم النتائج
            document.getElementById('resultsCard').style.display = 'block';
            document.getElementById('noResultsCard').style.display = 'none';
            document.getElementById('resultsCard').scrollIntoView({ behavior: 'smooth' });
        } else {
            alert('خطأ في استقبال البيانات من الخادم');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الاتصال:', error);
        alert('خطأ في الاتصال بالخادم: ' + error.message);
    })
    .finally(() => {
        // إعادة تعيين زر الحساب
        calculateBtn.innerHTML = originalText;
        calculateBtn.disabled = false;
    });
}



function displayRequirements(req) {
    console.log('📊 عرض البيانات:', req);

    // الملخص السريع
    document.getElementById('totalFeedKg').textContent = req.total_dm ? req.total_dm.toFixed(1) : '0.0';
    document.getElementById('totalProteinKg').textContent = req.total_protein ? (req.total_protein / 1000).toFixed(2) : '0.0';
    document.getElementById('totalEnergyMcal').textContent = req.total_energy ? req.total_energy.toFixed(1) : '0.0';
    document.getElementById('dailyCostJD').textContent = req.daily_cost ? req.daily_cost.toFixed(2) : '0.0';

    // تفاصيل المادة الجافة - استخدام القيم المحسوبة أو قيم افتراضية
    const totalDM = req.total_dm || 0;
    const maintenanceDM = totalDM * 0.6; // تقدير 60% للصيانة
    const productionDM = totalDM * 0.35; // تقدير 35% للإنتاج
    const activityDM = totalDM * 0.05; // تقدير 5% للنشاط

    document.getElementById('maintenanceDM').textContent = maintenanceDM.toFixed(1) + ' كغ';
    document.getElementById('productionDM').textContent = productionDM.toFixed(1) + ' كغ';
    document.getElementById('activityDM').textContent = activityDM.toFixed(1) + ' كغ';
    document.getElementById('totalDM').textContent = totalDM.toFixed(1) + ' كغ';

    // تفاصيل البروتين
    const totalProtein = req.total_protein || 0;
    const maintenanceProtein = totalProtein * 0.4; // تقدير 40% للصيانة
    const productionProtein = totalProtein * 0.6; // تقدير 60% للإنتاج

    document.getElementById('maintenanceProtein').textContent = maintenanceProtein.toFixed(0) + ' غ';
    document.getElementById('productionProtein').textContent = productionProtein.toFixed(0) + ' غ';
    document.getElementById('totalProtein').textContent = totalProtein.toFixed(0) + ' غ';
    document.getElementById('proteinPercentage').textContent = req.protein_percentage ? req.protein_percentage.toFixed(1) + '%' : '0.0%';

    // تفاصيل الطاقة
    const totalEnergy = req.total_energy || 0;
    const maintenanceEnergy = totalEnergy * 0.5; // تقدير 50% للصيانة
    const productionEnergy = totalEnergy * 0.45; // تقدير 45% للإنتاج
    const activityEnergy = totalEnergy * 0.05; // تقدير 5% للنشاط

    document.getElementById('maintenanceEnergy').textContent = maintenanceEnergy.toFixed(1) + ' Mcal';
    document.getElementById('productionEnergy').textContent = productionEnergy.toFixed(1) + ' Mcal';
    document.getElementById('activityEnergy').textContent = activityEnergy.toFixed(1) + ' Mcal';
    document.getElementById('totalEnergy').textContent = totalEnergy.toFixed(1) + ' Mcal';

    // تفاصيل الألياف
    const minFiber = req.min_fiber || (totalDM * 0.15);
    const maxFiber = req.max_fiber || (totalDM * 0.30);

    document.getElementById('minFiber').textContent = minFiber.toFixed(1) + ' كغ';
    document.getElementById('maxFiber').textContent = maxFiber.toFixed(1) + ' كغ';

    // توزيع العلف
    const feedDist = req.feed_distribution || {};
    const concentrateFeed = feedDist.concentrate || (totalDM * 0.65);
    const roughageFeed = feedDist.roughage || (totalDM * 0.30);
    const supplementFeed = feedDist.supplement || (totalDM * 0.05);

    document.getElementById('concentrateFeed').textContent = concentrateFeed.toFixed(1) + ' كغ';
    document.getElementById('roughageFeed').textContent = roughageFeed.toFixed(1) + ' كغ';
    document.getElementById('supplementFeed').textContent = supplementFeed.toFixed(1) + ' كغ';

    // تفاصيل التكلفة
    const dailyCost = req.daily_cost || 0;
    const monthlyCost = dailyCost * 30;
    const yearlyCost = dailyCost * 365;
    const milkProduction = parseFloat(document.getElementById('milkProduction').value) || 0;
    const costPerLiter = milkProduction > 0 ? dailyCost / milkProduction : 0;
    const costPerKg = 0.45; // متوسط تكلفة الكيلو
    const feedEfficiency = milkProduction > 0 ? totalDM / milkProduction : 0;

    document.getElementById('dailyCost').textContent = dailyCost.toFixed(2) + ' ريال';
    document.getElementById('monthlyCost').textContent = monthlyCost.toFixed(2) + ' ريال';
    document.getElementById('yearlyCost').textContent = yearlyCost.toFixed(2) + ' ريال';
    document.getElementById('costPerLiter').textContent = costPerLiter.toFixed(3) + ' ريال';
    document.getElementById('costPerKg').textContent = costPerKg.toFixed(3) + ' ريال';
    document.getElementById('feedEfficiency').textContent = feedEfficiency.toFixed(2);
}

function suggestMix() {
    if (!currentRequirements) {
        alert('يرجى حساب الاحتياجات أولاً');
        return;
    }
    
    fetch('/suggest_mix_for_cow', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            requirements: currentRequirements
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('خطأ: ' + data.error);
            return;
        }
        
        currentSuggestedMix = data.suggested_mix;
        displaySuggestedMix(data.suggested_mix);
        document.getElementById('suggestedMixCard').style.display = 'block';
        document.getElementById('suggestedMixCard').scrollIntoView({ behavior: 'smooth' });

        // إخفاء أي رسائل خطأ سابقة
        const errorMessages = document.querySelectorAll('.alert-danger');
        errorMessages.forEach(msg => msg.remove());
    })
    .catch(error => {
        console.error('Error:', error);

        // إظهار رسالة خطأ في البطاقة بدلاً من alert
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger mt-3';
        errorDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>خطأ في اقتراح الخلطة:</strong> ${error.message || 'حدث خطأ غير متوقع'}
        `;

        // إضافة رسالة الخطأ بعد أزرار العمليات
        const operationsRow = document.querySelector('.row.mt-4');
        operationsRow.parentNode.insertBefore(errorDiv, operationsRow.nextSibling);

        // إزالة رسالة الخطأ بعد 5 ثوان
        setTimeout(() => {
            errorDiv.remove();
        }, 5000);
    });
}

function displaySuggestedMix(suggestedMix) {
    // عرض ملخص الخلطة
    document.getElementById('mixTotalWeight').textContent = suggestedMix.totals.weight + ' كغ';
    document.getElementById('mixTotalCost').textContent = suggestedMix.totals.cost + ' د.أ';
    document.getElementById('mixCostPerKg').textContent = suggestedMix.totals.cost_per_kg + ' د.أ';
    
    // عرض مؤشرات الجودة
    document.getElementById('proteinMatch').textContent = suggestedMix.quality_indicators.protein_match + '%';
    document.getElementById('energyMatch').textContent = suggestedMix.quality_indicators.energy_match + '%';
    document.getElementById('costEfficiency').textContent = suggestedMix.quality_indicators.cost_efficiency;
    
    // عرض مكونات الخلطة
    const tableBody = document.getElementById('mixComponentsTable');
    tableBody.innerHTML = '';
    
    suggestedMix.mix_components.forEach(component => {
        const row = document.createElement('tr');
        const cost = component.quantity * component.ingredient.price_per_kg;
        row.innerHTML = `
            <td>
                <strong>${component.ingredient.name}</strong>
                <br><small class="text-muted">${component.ingredient.protein}% بروتين، ${component.ingredient.energy} kcal/kg</small>
            </td>
            <td class="text-center">${component.quantity.toFixed(2)}</td>
            <td class="text-center">${component.percentage.toFixed(1)}%</td>
            <td class="text-center">${cost.toFixed(3)}</td>
            <td><small class="text-info">${component.reason}</small></td>
        `;
        tableBody.appendChild(row);
    });
    
    // عرض التوصيات
    const recommendationsList = document.getElementById('recommendationsList');
    recommendationsList.innerHTML = '';
    
    suggestedMix.recommendations.forEach(rec => {
        const alertClass = rec.type === 'good' ? 'alert-success' : 
                          rec.type.includes('low') ? 'alert-warning' : 'alert-info';
        
        const recDiv = document.createElement('div');
        recDiv.className = `alert ${alertClass} mb-2`;
        recDiv.innerHTML = `
            <strong>${rec.message}</strong><br>
            <small>${rec.suggestion}</small>
        `;
        recommendationsList.appendChild(recDiv);
    });
}

function showSaveModal() {
    if (!currentRequirements) {
        alert('يرجى حساب الاحتياجات أولاً');
        return;
    }
    
    // ملء البيانات في النافذة
    document.getElementById('modalWeight').textContent = document.getElementById('cowWeight').value;
    document.getElementById('modalMilk').textContent = document.getElementById('milkProduction').value;
    document.getElementById('modalFeed').textContent = currentRequirements.total_dm;
    document.getElementById('modalCost').textContent = currentRequirements.daily_cost;
    
    saveModal.show();
}

function saveRequirements() {
    let cowName = document.getElementById('cowNameInput').value.trim();
    if (!cowName) {
        // استخدم اسم البقرة من النموذج الرئيسي إذا لم يتم إدخاله في النافذة
        cowName = document.getElementById('cowName').value.trim();
        if (!cowName) {
            cowName = 'بقرة بدون اسم - ' + new Date().toLocaleDateString();
        }
    }
    
    const saveData = {
        cow_name: cowName,
        weight: parseFloat(document.getElementById('cowWeight').value),
        milk_production: parseFloat(document.getElementById('milkProduction').value),
        lactation_stage: document.getElementById('lactationStage').value,
        activity_level: document.getElementById('activityLevel').value,
        requirements: currentRequirements
    };
    
    fetch('/save_cow_calculation', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(saveData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('خطأ: ' + data.error);
            return;
        }
        
        alert(data.message);
        saveModal.hide();
        document.getElementById('cowNameInput').value = '';
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في حفظ البيانات');
    });
}

function acceptMix() {
    if (!currentSuggestedMix) {
        alert('لا توجد خلطة مقترحة للقبول');
        return;
    }
    
    // تحويل المستخدم لصفحة تكوين الخلطات مع البيانات المقترحة
    const mixData = {
        name: 'خلطة مقترحة للبقرة',
        components: currentSuggestedMix.mix_components
    };
    
    // حفظ البيانات في Local Storage للانتقال
    localStorage.setItem('suggestedMixData', JSON.stringify(mixData));
    
    // الانتقال للصفحة الرئيسية
    window.location.href = '/';
}

function modifyMix() {
    if (!currentSuggestedMix) {
        alert('لا توجد خلطة مقترحة للتعديل');
        return;
    }
    
    // تحويل المستخدم لصفحة تكوين الخلطات مع إمكانية التعديل
    acceptMix();
}

function printReport() {
    window.print();
}
</script>
{% endblock %}