
    <!DOCTYPE html>
    <html>
    <head>
        <title>اختبار JavaScript</title>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    </head>
    <body>
        <input type="number" id="cowWeight" value="500">
        <input type="number" id="milkProduction" value="25">
        <select id="lactationStage">
            <option value="mid" selected>متوسطة</option>
        </select>
        <select id="activityLevel">
            <option value="moderate" selected>متوسط</option>
        </select>
        <button id="calculateBtn">حساب</button>
        <div id="resultsCard" style="display: none;"></div>
        <div id="noResultsCard"></div>
        
        <script>
        function calculateNutritionalRequirements(weight, milk, stage, activity) {
            const stageFactors = {
                'early': { dm: 1.1, protein: 1.15, energy: 1.1 },
                'mid': { dm: 1.0, protein: 1.0, energy: 1.0 },
                'late': { dm: 0.95, protein: 0.9, energy: 0.95 }
            };
            
            const activityFactors = {
                'low': { dm: 0.95, protein: 0.95, energy: 0.95 },
                'moderate': { dm: 1.0, protein: 1.0, energy: 1.0 },
                'high': { dm: 1.1, protein: 1.1, energy: 1.1 }
            };
            
            const baseDM = 0.025 * weight + 0.1 * milk;
            const baseProtein = weight * 0.6 + milk * 58;
            const baseEnergy = 1.42 + 0.0115 * weight + 0.46 * milk;
            
            const stageFactor = stageFactors[stage] || stageFactors['mid'];
            const activityFactor = activityFactors[activity] || activityFactors['moderate'];
            
            const totalDM = baseDM * stageFactor.dm * activityFactor.dm;
            const totalProtein = baseProtein * stageFactor.protein * activityFactor.protein;
            const totalEnergy = baseEnergy * stageFactor.energy * activityFactor.energy;
            
            return {
                total_dm: totalDM,
                total_protein: totalProtein,
                total_energy: totalEnergy,
                protein_percentage: (totalProtein / 1000 / totalDM) * 100,
                daily_cost: totalDM * 0.4
            };
        }
        
        function testCalculation() {
            try {
                const weight = 500;
                const milk = 25;
                const stage = 'mid';
                const activity = 'moderate';
                
                const result = calculateNutritionalRequirements(weight, milk, stage, activity);
                console.log('نتيجة الاختبار:', result);
                
                if (result.total_dm > 0 && result.total_protein > 0 && result.total_energy > 0) {
                    console.log('✅ دالة الحساب تعمل بشكل صحيح');
                    return true;
                } else {
                    console.log('❌ دالة الحساب تعطي نتائج خاطئة');
                    return false;
                }
            } catch (error) {
                console.log('❌ خطأ في دالة الحساب:', error);
                return false;
            }
        }
        
        // تشغيل الاختبار
        window.onload = function() {
            testCalculation();
        };
        </script>
    </body>
    </html>
    