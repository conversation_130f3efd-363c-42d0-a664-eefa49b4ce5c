#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_cow_requirements_page():
    """اختبار صفحة احتياجات البقرة"""
    
    base_url = "http://localhost:5000"
    
    print("🧪 اختبار صفحة احتياجات البقرة...")
    print("=" * 50)
    
    # اختبار الوصول للصفحة
    print("1. اختبار الوصول للصفحة...")
    try:
        response = requests.get(f"{base_url}/cow_requirements")
        
        if response.status_code == 200:
            print("   ✅ الصفحة تعمل بشكل صحيح")
            
            # التحقق من وجود العناصر المهمة
            content = response.text
            
            elements_to_check = [
                ("calculateBtn", "زر حساب الاحتياجات"),
                ("cowWeight", "حقل الوزن"),
                ("milkProduction", "حقل إنتاج الحليب"),
                ("lactationStage", "قائمة مرحلة الإنتاج"),
                ("activityLevel", "قائمة مستوى النشاط"),
                ("resultsCard", "بطاقة النتائج"),
                ("calculateNutritionalRequirements", "دالة حساب الاحتياجات"),
                ("displayRequirements", "دالة عرض النتائج")
            ]
            
            for element, description in elements_to_check:
                if element in content:
                    print(f"   ✅ {description} موجود")
                else:
                    print(f"   ❌ {description} مفقود")
            
            return True
        else:
            print(f"   ❌ فشل في الوصول للصفحة: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في الوصول للصفحة: {e}")
        return False

def test_calculation_api():
    """اختبار API حساب الاحتياجات"""
    
    base_url = "http://localhost:5000"
    
    print("\n2. اختبار API حساب الاحتياجات...")
    
    # بيانات البقرة للاختبار
    cow_data = {
        "animal_type": "cow",
        "cow_weight": 500,
        "cow_milk": 25,
        "cow_stage": "mid",
        "activity_level": "moderate"
    }
    
    try:
        response = requests.post(f"{base_url}/calculate_animal_requirements", json=cow_data)
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ API يعمل بشكل صحيح")
            
            if 'requirements' in data:
                requirements = data['requirements']
                print(f"   📊 المادة الجافة: {requirements.get('total_dm', 'غير محدد')} كغ/يوم")
                print(f"   🥩 البروتين: {requirements.get('total_protein', 'غير محدد')} غرام/يوم")
                print(f"   ⚡ الطاقة: {requirements.get('total_energy', 'غير محدد')} Mcal/يوم")
                print(f"   💰 التكلفة: {requirements.get('daily_cost', 'غير محدد')} ريال/يوم")
                return True
            else:
                print("   ❌ البيانات المرجعة غير صحيحة")
                return False
        else:
            print(f"   ❌ فشل في API: {response.status_code}")
            print(f"   📝 الرد: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في API: {e}")
        return False

def test_javascript_functionality():
    """اختبار وظائف JavaScript"""
    
    print("\n3. اختبار وظائف JavaScript...")
    
    # إنشاء ملف HTML بسيط لاختبار JavaScript
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>اختبار JavaScript</title>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    </head>
    <body>
        <input type="number" id="cowWeight" value="500">
        <input type="number" id="milkProduction" value="25">
        <select id="lactationStage">
            <option value="mid" selected>متوسطة</option>
        </select>
        <select id="activityLevel">
            <option value="moderate" selected>متوسط</option>
        </select>
        <button id="calculateBtn">حساب</button>
        <div id="resultsCard" style="display: none;"></div>
        <div id="noResultsCard"></div>
        
        <script>
        function calculateNutritionalRequirements(weight, milk, stage, activity) {
            const stageFactors = {
                'early': { dm: 1.1, protein: 1.15, energy: 1.1 },
                'mid': { dm: 1.0, protein: 1.0, energy: 1.0 },
                'late': { dm: 0.95, protein: 0.9, energy: 0.95 }
            };
            
            const activityFactors = {
                'low': { dm: 0.95, protein: 0.95, energy: 0.95 },
                'moderate': { dm: 1.0, protein: 1.0, energy: 1.0 },
                'high': { dm: 1.1, protein: 1.1, energy: 1.1 }
            };
            
            const baseDM = 0.025 * weight + 0.1 * milk;
            const baseProtein = weight * 0.6 + milk * 58;
            const baseEnergy = 1.42 + 0.0115 * weight + 0.46 * milk;
            
            const stageFactor = stageFactors[stage] || stageFactors['mid'];
            const activityFactor = activityFactors[activity] || activityFactors['moderate'];
            
            const totalDM = baseDM * stageFactor.dm * activityFactor.dm;
            const totalProtein = baseProtein * stageFactor.protein * activityFactor.protein;
            const totalEnergy = baseEnergy * stageFactor.energy * activityFactor.energy;
            
            return {
                total_dm: totalDM,
                total_protein: totalProtein,
                total_energy: totalEnergy,
                protein_percentage: (totalProtein / 1000 / totalDM) * 100,
                daily_cost: totalDM * 0.4
            };
        }
        
        function testCalculation() {
            try {
                const weight = 500;
                const milk = 25;
                const stage = 'mid';
                const activity = 'moderate';
                
                const result = calculateNutritionalRequirements(weight, milk, stage, activity);
                console.log('نتيجة الاختبار:', result);
                
                if (result.total_dm > 0 && result.total_protein > 0 && result.total_energy > 0) {
                    console.log('✅ دالة الحساب تعمل بشكل صحيح');
                    return true;
                } else {
                    console.log('❌ دالة الحساب تعطي نتائج خاطئة');
                    return false;
                }
            } catch (error) {
                console.log('❌ خطأ في دالة الحساب:', error);
                return false;
            }
        }
        
        // تشغيل الاختبار
        window.onload = function() {
            testCalculation();
        };
        </script>
    </body>
    </html>
    """
    
    # حفظ ملف الاختبار
    with open('test_js.html', 'w', encoding='utf-8') as f:
        f.write(test_html)
    
    print("   📝 تم إنشاء ملف اختبار JavaScript: test_js.html")
    print("   💡 افتح الملف في المتصفح وتحقق من console للنتائج")
    
    return True

if __name__ == "__main__":
    print("🚀 بدء اختبار صفحة احتياجات البقرة...")
    print("🎯 الهدف: تشخيص مشكلة عدم عمل زر 'حساب الاحتياجات'")
    print()
    
    # تشغيل الاختبارات
    page_ok = test_cow_requirements_page()
    api_ok = test_calculation_api()
    js_ok = test_javascript_functionality()
    
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    print(f"   الصفحة: {'✅ تعمل' if page_ok else '❌ لا تعمل'}")
    print(f"   API: {'✅ يعمل' if api_ok else '❌ لا يعمل'}")
    print(f"   JavaScript: {'✅ تم إنشاء اختبار' if js_ok else '❌ فشل'}")
    
    if not page_ok or not api_ok:
        print("\n⚠️ هناك مشاكل في الخادم تحتاج إصلاح")
    else:
        print("\n💡 المشكلة قد تكون في JavaScript. تحقق من:")
        print("   1. أخطاء JavaScript في console المتصفح")
        print("   2. تحميل مكتبات Bootstrap بشكل صحيح")
        print("   3. تضارب في أسماء الدوال أو المتغيرات")
        print("   4. أخطاء في بناء الجملة (syntax errors)")
        
    print(f"\n🌐 رابط الصفحة: http://localhost:5000/cow_requirements")
    print("🔧 افتح Developer Tools (F12) وتحقق من تبويب Console للأخطاء")
