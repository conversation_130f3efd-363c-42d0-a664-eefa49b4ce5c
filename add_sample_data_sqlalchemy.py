#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إضافة بيانات تجريبية لقاعدة البيانات SQLAlchemy
"""

from app import app, db, CowCalculation, Ingredient, Mix
from datetime import datetime, timedelta
import random

def add_sample_cow_calculations():
    """إضافة حسابات أبقار تجريبية"""
    print("🐄 إضافة بيانات تجريبية للأبقار...")
    
    with app.app_context():
        # حذف البيانات القديمة
        CowCalculation.query.delete()
        
        # بيانات أبقار تجريبية
        sample_cows = [
            ('فاطمة', 550, 28, 'mid', 'moderate', 24.5, 1920, 29.2, 9.8),
            ('خديجة', 480, 22, 'early', 'moderate', 21.8, 1680, 25.8, 8.7),
            ('عائشة', 620, 35, 'mid', 'high', 28.2, 2280, 33.5, 11.3),
            ('زينب', 500, 18, 'late', 'low', 19.5, 1450, 22.8, 7.8),
            ('مريم', 580, 30, 'mid', 'moderate', 26.1, 2050, 31.2, 10.4),
            ('حفصة', 460, 20, 'early', 'moderate', 20.2, 1580, 24.1, 8.1),
            ('أمينة', 520, 25, 'mid', 'moderate', 23.2, 1850, 27.5, 9.2),
            ('صفية', 490, 19, 'late', 'low', 18.8, 1420, 21.9, 7.5),
            ('نور', 545, 26, 'mid', 'moderate', 23.8, 1890, 28.1, 9.5),
            ('سارة', 505, 21, 'early', 'high', 22.1, 1720, 26.3, 8.9)
        ]
        
        # إضافة البيانات
        for cow_data in sample_cows:
            # تاريخ متنوع
            date_offset = timedelta(days=random.randint(1, 30), hours=random.randint(0, 23))
            calc_date = datetime.now() - date_offset
            
            cow_calc = CowCalculation(
                cow_name=cow_data[0],
                weight=cow_data[1],
                milk_production=cow_data[2],
                lactation_stage=cow_data[3],
                activity_level=cow_data[4],
                total_dm=cow_data[5],
                total_protein=cow_data[6],
                total_energy=cow_data[7],
                daily_cost=cow_data[8],
                calculation_date=calc_date
            )
            
            db.session.add(cow_calc)
        
        # حفظ البيانات
        db.session.commit()
        
        # التحقق من البيانات
        count = CowCalculation.query.count()
        print(f"✅ تم إدراج {count} سجل بقرة")
        
        # عرض عينة من البيانات
        print("\n📊 عينة من البيانات المضافة:")
        recent_cows = CowCalculation.query.order_by(CowCalculation.calculation_date.desc()).limit(5).all()
        
        for cow in recent_cows:
            print(f"   - {cow.cow_name}: {cow.weight}كغ، {cow.milk_production}ل/يوم، {cow.daily_cost}د.أ/يوم")

def check_database():
    """فحص قاعدة البيانات"""
    print("\n🔍 فحص قاعدة البيانات...")
    
    with app.app_context():
        try:
            # فحص الجداول
            tables_info = [
                ('CowCalculation', CowCalculation.query.count()),
                ('Ingredient', Ingredient.query.count()),
                ('Mix', Mix.query.count())
            ]
            
            print(f"📁 الجداول الموجودة:")
            for table_name, count in tables_info:
                print(f"   - {table_name}: {count} سجل")
                
        except Exception as e:
            print(f"❌ خطأ في فحص قاعدة البيانات: {e}")

def main():
    """الدالة الرئيسية"""
    print("🗃️ إعداد بيانات تجريبية لمقارنة الأبقار (SQLAlchemy)")
    print("=" * 60)
    
    add_sample_cow_calculations()
    check_database()
    
    print("\n" + "=" * 60)
    print("✅ تم إعداد البيانات بنجاح!")
    print("\n📱 يمكنك الآن:")
    print("1. فتح http://localhost:5000/compare_cows")
    print("2. اختيار أبقار للمقارنة")
    print("3. عرض النتائج والإحصائيات")
    print("4. مقارنة الكفاءة والتكلفة")

if __name__ == "__main__":
    main()