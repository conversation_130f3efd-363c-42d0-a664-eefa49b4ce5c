#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار وإضافة بيانات تجريبية لمقارنة الأبقار
"""

from app_simple import app, get_db, init_db
import sqlite3
from datetime import datetime, timedelta
import random

def add_sample_cow_calculations():
    """إضافة حسابات تجريبية للأبقار"""
    print("🐄 إضافة بيانات تجريبية للأبقار...")
    
    # إنشاء قاعدة البيانات
    init_db()
    
    conn = get_db()
    
    # بيانات أبقار تجريبية
    sample_cows = [
        {
            'cow_name': 'فاطمة',
            'weight': 550,
            'milk_production': 28,
            'lactation_stage': 'mid',
            'total_dm': 24.5,
            'total_protein': 1920,
            'total_energy': 29.2,
            'daily_cost': 9.8
        },
        {
            'cow_name': 'خديجة',
            'weight': 480,
            'milk_production': 22,
            'lactation_stage': 'early',
            'total_dm': 21.8,
            'total_protein': 1680,
            'total_energy': 25.8,
            'daily_cost': 8.7
        },
        {
            'cow_name': 'عائشة',
            'weight': 620,
            'milk_production': 35,
            'lactation_stage': 'mid',
            'total_dm': 28.2,
            'total_protein': 2280,
            'total_energy': 33.5,
            'daily_cost': 11.3
        },
        {
            'cow_name': 'زينب',
            'weight': 500,
            'milk_production': 18,
            'lactation_stage': 'late',
            'total_dm': 19.5,
            'total_protein': 1450,
            'total_energy': 22.8,
            'daily_cost': 7.8
        },
        {
            'cow_name': 'مريم',
            'weight': 580,
            'milk_production': 30,
            'lactation_stage': 'mid',
            'total_dm': 26.1,
            'total_protein': 2050,
            'total_energy': 31.2,
            'daily_cost': 10.4
        },
        {
            'cow_name': 'حفصة',
            'weight': 460,
            'milk_production': 20,
            'lactation_stage': 'early',
            'total_dm': 20.2,
            'total_protein': 1580,
            'total_energy': 24.1,
            'daily_cost': 8.1
        }
    ]
    
    # حذف البيانات القديمة
    conn.execute('DELETE FROM cow_calculations')
    
    # إدراج البيانات الجديدة
    for i, cow in enumerate(sample_cows):
        # تواريخ متنوعة
        date_offset = timedelta(days=random.randint(1, 30))
        calc_date = (datetime.now() - date_offset).strftime('%Y-%m-%d %H:%M:%S')
        
        conn.execute('''
            INSERT INTO cow_calculations 
            (cow_name, weight, milk_production, lactation_stage, total_dm, 
             total_protein, total_energy, daily_cost, calculation_date)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            cow['cow_name'],
            cow['weight'],
            cow['milk_production'],
            cow['lactation_stage'],
            cow['total_dm'],
            cow['total_protein'],
            cow['total_energy'],
            cow['daily_cost'],
            calc_date
        ))
    
    conn.commit()
    conn.close()
    
    print("✅ تم إضافة 6 أبقار تجريبية بنجاح!")

def test_compare_endpoint():
    """اختبار endpoint مقارنة الأبقار"""
    print("\n🔧 اختبار مقارنة الأبقار...")
    
    with app.test_client() as client:
        # اختبار صفحة المقارنة
        response = client.get('/compare_cows')
        print(f"   - كود الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ صفحة مقارنة الأبقار تعمل بنجاح!")
            
            # فحص المحتوى
            content = response.get_data(as_text=True)
            if 'فاطمة' in content:
                print("✅ البيانات التجريبية تظهر في الصفحة!")
            else:
                print("⚠️ البيانات التجريبية لا تظهر")
                
        else:
            print(f"❌ فشل في تحميل صفحة المقارنة: {response.status_code}")
            print(f"   الخطأ: {response.get_data(as_text=True)}")

def test_cow_history_endpoint():
    """اختبار endpoint تاريخ الأبقار"""
    print("\n🔧 اختبار تاريخ الأبقار...")
    
    with app.test_client() as client:
        response = client.get('/cow_history')
        print(f"   - كود الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ صفحة تاريخ الأبقار تعمل بنجاح!")
        else:
            print(f"❌ فشل في تحميل صفحة التاريخ: {response.status_code}")

def test_saved_mixes_endpoint():
    """اختبار endpoint الخلطات المحفوظة"""
    print("\n🔧 اختبار الخلطات المحفوظة...")
    
    with app.test_client() as client:
        response = client.get('/saved_mixes')
        print(f"   - كود الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ صفحة الخلطات المحفوظة تعمل بنجاح!")
        else:
            print(f"❌ فشل في تحميل صفحة الخلطات: {response.status_code}")

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار مقارنة الأبقار")
    print("=" * 50)
    
    # إضافة البيانات التجريبية
    add_sample_cow_calculations()
    
    # اختبار المسارات
    test_compare_endpoint()
    test_cow_history_endpoint()
    test_saved_mixes_endpoint()
    
    print("\n" + "=" * 50)
    print("🎯 الاختبار مكتمل!")
    print("\n💡 الآن يمكنك:")
    print("1. تشغيل البرنامج: python run.py")
    print("2. زيارة: http://localhost:5000/compare_cows")
    print("3. اختيار أبقار للمقارنة")
    print("4. مشاهدة النتائج والإحصائيات")

if __name__ == "__main__":
    main()