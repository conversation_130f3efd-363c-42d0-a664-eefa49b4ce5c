#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time

def test_full_workflow():
    """اختبار سير العمل الكامل لاقتراح الخلطة"""
    
    base_url = "http://localhost:5000"
    
    print("🧪 اختبار سير العمل الكامل...")
    print("=" * 50)
    
    # الخطوة 1: حساب احتياجات البقرة
    print("1. حساب احتياجات البقرة...")
    
    cow_data = {
        "animal_type": "cow",
        "cow_weight": 550,
        "cow_milk": 25,
        "cow_stage": "mid",
        "activity_level": "moderate"
    }
    
    try:
        response = requests.post(f"{base_url}/calculate_animal_requirements", json=cow_data)
        
        if response.status_code == 200:
            requirements_data = response.json()
            print("   ✅ تم حساب الاحتياجات بنجاح")
            
            requirements = requirements_data['requirements']
            print(f"   📊 المادة الجافة: {requirements['total_dm']} كغ/يوم")
            print(f"   🥩 البروتين: {requirements['total_protein']} غرام/يوم")
            print(f"   ⚡ الطاقة: {requirements['total_energy']} Mcal/يوم")
            print(f"   💰 التكلفة: {requirements['daily_cost']} ريال/يوم")
            
        else:
            print(f"   ❌ فشل في حساب الاحتياجات: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في حساب الاحتياجات: {e}")
        return False
    
    # الخطوة 2: اقتراح الخلطة
    print("\n2. اقتراح الخلطة...")
    
    try:
        response = requests.post(f"{base_url}/suggest_mix_for_cow", 
                               json={"requirements": requirements})
        
        if response.status_code == 200:
            mix_data = response.json()
            
            if mix_data.get('success'):
                print("   ✅ تم اقتراح الخلطة بنجاح")
                
                suggested_mix = mix_data['suggested_mix']
                
                print(f"   📋 عدد المكونات: {len(suggested_mix['mix_components'])}")
                print(f"   💰 إجمالي التكلفة: {suggested_mix['totals']['cost']} ريال")
                print(f"   📊 دقة البروتين: {suggested_mix['quality_indicators']['protein_match']}%")
                print(f"   📊 دقة الطاقة: {suggested_mix['quality_indicators']['energy_match']}%")
                
                # عرض المكونات
                print("\n   🌾 مكونات الخلطة:")
                for i, component in enumerate(suggested_mix['mix_components'], 1):
                    ingredient = component['ingredient']
                    print(f"      {i}. {ingredient['name']}: {component['quantity']} كغ ({component['percentage']}%)")
                
                return suggested_mix
            else:
                print(f"   ❌ فشل في اقتراح الخلطة: {mix_data.get('error', 'خطأ غير محدد')}")
                return False
        else:
            print(f"   ❌ فشل في طلب الخلطة: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في اقتراح الخلطة: {e}")
        return False

def test_mix_saving():
    """اختبار حفظ الخلطة"""
    
    base_url = "http://localhost:5000"
    
    print("\n3. اختبار حفظ الخلطة...")
    
    # بيانات خلطة تجريبية
    mix_data = {
        "name": "خلطة تجريبية من اقتراح البقرة",
        "ingredients": [
            {"ingredient_id": 1, "quantity": 3.64},
            {"ingredient_id": 2, "quantity": 3.64},
            {"ingredient_id": 3, "quantity": 2.18},
            {"ingredient_id": 4, "quantity": 2.18},
            {"ingredient_id": 5, "quantity": 5.70},
            {"ingredient_id": 6, "quantity": 5.70},
            {"ingredient_id": 7, "quantity": 1.21}
        ]
    }
    
    try:
        response = requests.post(f"{base_url}/save_mix", json=mix_data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("   ✅ تم حفظ الخلطة بنجاح")
                return True
            else:
                print(f"   ❌ فشل في حفظ الخلطة: {result.get('error', 'خطأ غير محدد')}")
                return False
        else:
            print(f"   ❌ فشل في طلب الحفظ: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في حفظ الخلطة: {e}")
        return False

def test_saved_mixes():
    """اختبار عرض الخلطات المحفوظة"""
    
    base_url = "http://localhost:5000"
    
    print("\n4. اختبار عرض الخلطات المحفوظة...")
    
    try:
        response = requests.get(f"{base_url}/saved_mixes")
        
        if response.status_code == 200:
            print("   ✅ صفحة الخلطات المحفوظة تعمل بشكل صحيح")
            return True
        else:
            print(f"   ❌ فشل في الوصول لصفحة الخلطات: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في الوصول لصفحة الخلطات: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار سير العمل الكامل...")
    print("🎯 الهدف: اختبار العملية من البداية للنهاية")
    print()
    
    # تشغيل الاختبارات
    suggested_mix = test_full_workflow()
    
    if suggested_mix:
        # اختبار حفظ الخلطة
        save_ok = test_mix_saving()
        
        # اختبار عرض الخلطات المحفوظة
        view_ok = test_saved_mixes()
        
        print("\n" + "=" * 50)
        print("📊 ملخص النتائج:")
        print(f"   حساب الاحتياجات: ✅ يعمل")
        print(f"   اقتراح الخلطة: ✅ يعمل")
        print(f"   حفظ الخلطة: {'✅ يعمل' if save_ok else '❌ لا يعمل'}")
        print(f"   عرض الخلطات: {'✅ يعمل' if view_ok else '❌ لا يعمل'}")
        
        if save_ok and view_ok:
            print("\n🎉 جميع الوظائف تعمل بشكل مثالي!")
            print("💡 الآن يمكنك:")
            print("   1. فتح صفحة احتياجات البقرة: http://localhost:5000/cow_requirements")
            print("   2. إدخال بيانات البقرة وحساب الاحتياجات")
            print("   3. الضغط على 'اقتراح خلطة مناسبة'")
            print("   4. الضغط على 'قبول الخلطة وحفظها' أو 'تعديل الخلطة'")
            print("   5. مراجعة الخلطات المحفوظة: http://localhost:5000/saved_mixes")
        else:
            print("\n⚠️ بعض الوظائف تحتاج إصلاح")
    else:
        print("\n❌ فشل في اختبار سير العمل الأساسي")
