{% extends "base.html" %}

{% block title %}الصفحة الرئيسية - برنامج تكوين الأعلاف المركزة{% endblock %}

{% block content %}
<div class="row">
    <!-- قسم إضافة مكون جديد -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-plus me-2"></i>
                    إضافة مكون جديد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('add_ingredient') }}">
                    <div class="mb-3">
                        <label for="name" class="form-label">اسم المكون</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="protein" class="form-label">نسبة البروتين (%)</label>
                        <input type="number" step="0.1" class="form-control" id="protein" name="protein" required>
                    </div>
                    <div class="mb-3">
                        <label for="energy" class="form-label">الطاقة (kcal/kg)</label>
                        <input type="number" step="0.1" class="form-control" id="energy" name="energy" required>
                    </div>
                    <div class="mb-3">
                        <label for="fiber" class="form-label">نسبة الألياف (%)</label>
                        <input type="number" step="0.1" class="form-control" id="fiber" name="fiber" required>
                    </div>
                    <div class="mb-3">
                        <label for="price_per_kg" class="form-label">السعر/كغ (دينار أردني)</label>
                        <input type="number" step="0.001" class="form-control" id="price_per_kg" name="price_per_kg" required>
                    </div>
                    <button type="submit" class="btn btn-success w-100">
                        <i class="fas fa-plus me-2"></i>
                        إضافة المكون
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- قسم تكوين الخلطة -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-balance-scale me-2"></i>
                    تكوين خلطة الأعلاف
                </h5>
            </div>
            <div class="card-body">
                <!-- قسم إضافة مكون للخلطة -->
                <div class="card mb-4" style="background-color: #f8f9fa;">
                    <div class="card-body">
                        <h6 class="card-title text-primary">
                            <i class="fas fa-plus-circle me-2"></i>
                            إضافة مكون للخلطة
                        </h6>
                        <div class="row">
                            <div class="col-md-4">
                                <label for="ingredientSelect" class="form-label">اختيار المكون:</label>
                                <select class="form-select" id="ingredientSelect">
                                    <option value="">-- اختر مكون --</option>
                                    {% for ingredient in ingredients %}
                                    <option value="{{ ingredient.id }}" 
                                            data-protein="{{ ingredient.protein }}"
                                            data-energy="{{ ingredient.energy }}"
                                            data-fiber="{{ ingredient.fiber }}"
                                            data-price="{{ ingredient.price_per_kg }}">
                                        {{ ingredient.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="quantityInput" class="form-label">الكمية (كغ):</label>
                                <input type="number" step="0.1" min="0" class="form-control" 
                                       id="quantityInput" placeholder="0.0">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">معلومات المكون:</label>
                                <div class="row">
                                    <div class="col-3">
                                        <small class="text-muted">البروتين:</small>
                                        <div class="fw-bold text-danger" id="selectedProtein">-</div>
                                    </div>
                                    <div class="col-3">
                                        <small class="text-muted">الطاقة:</small>
                                        <div class="fw-bold text-warning" id="selectedEnergy">-</div>
                                    </div>
                                    <div class="col-3">
                                        <small class="text-muted">الألياف:</small>
                                        <div class="fw-bold text-success" id="selectedFiber">-</div>
                                    </div>
                                    <div class="col-3">
                                        <small class="text-muted">السعر/كغ:</small>
                                        <div class="fw-bold text-info" id="selectedPrice">-</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="button" class="btn btn-primary" id="addIngredientBtn" disabled>
                                    <i class="fas fa-plus me-2"></i>
                                    إضافة للخلطة
                                </button>
                                <button type="button" class="btn btn-outline-secondary ms-2" id="clearBtn">
                                    <i class="fas fa-eraser me-2"></i>
                                    مسح الكل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قائمة المكونات المضافة -->
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            المكونات في الخلطة (<span id="mixtureCount">0</span> مكون)
                        </h6>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="mixtureTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th style="width: 25%">المكون</th>
                                        <th style="width: 12%" class="text-center">البروتين %</th>
                                        <th style="width: 12%" class="text-center">الطاقة</th>
                                        <th style="width: 12%" class="text-center">الألياف %</th>
                                        <th style="width: 12%" class="text-center">السعر/كغ</th>
                                        <th style="width: 12%" class="text-center">الكمية (كغ)</th>
                                        <th style="width: 15%" class="text-center">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="mixtureTableBody">
                                    <tr id="emptyRow">
                                        <td colspan="7" class="text-center text-muted py-4">
                                            <i class="fas fa-inbox fa-2x mb-2 d-block"></i>
                                            لم يتم إضافة أي مكونات بعد
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- أزرار العمليات -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <button type="button" class="btn btn-info btn-lg w-100" id="calculateBtn" disabled>
                            <i class="fas fa-calculator me-2"></i>
                            حساب الخلطة
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button type="button" class="btn btn-success btn-lg w-100" id="saveBtn" disabled>
                            <i class="fas fa-save me-2"></i>
                            حفظ الخلطة
                        </button>
                    </div>
                </div>

                <!-- معاينة سريعة للوزن والتكلفة -->
                <div class="alert alert-info mt-3" id="quickPreview" style="display: none;">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <strong>إجمالي الوزن:</strong>
                            <span id="previewWeight" class="text-primary">0</span> كغ
                        </div>
                        <div class="col-md-4">
                            <strong>التكلفة المتوقعة:</strong>
                            <span id="previewCost" class="text-success">0</span> د.أ
                        </div>
                        <div class="col-md-4">
                            <strong>التكلفة/كغ:</strong>
                            <span id="previewCostPerKg" class="text-info">0</span> د.أ
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قسم عرض النتائج -->
<div class="row" id="resultsSection" style="display: none;">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    نتائج تحليل الخلطة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- ملخص النتائج -->
                    <div class="col-lg-6">
                        <h6 class="text-primary">ملخص الخلطة:</h6>
                        <table class="table table-bordered">
                            <tr>
                                <td><strong>إجمالي الوزن:</strong></td>
                                <td><span id="totalWeight">0</span> كغ</td>
                            </tr>
                            <tr>
                                <td><strong>إجمالي التكلفة:</strong></td>
                                <td><span id="totalCost">0</span> د.أ</td>
                            </tr>
                            <tr>
                                <td><strong>التكلفة/كغ:</strong></td>
                                <td><span id="costPerKg">0</span> د.أ</td>
                            </tr>
                        </table>
                    </div>
                    
                    <!-- التحليل الغذائي -->
                    <div class="col-lg-6">
                        <h6 class="text-success">التحليل الغذائي:</h6>
                        <table class="table table-bordered">
                            <tr>
                                <td><strong>متوسط البروتين:</strong></td>
                                <td><span id="avgProtein">0</span>%</td>
                            </tr>
                            <tr>
                                <td><strong>متوسط الطاقة:</strong></td>
                                <td><span id="avgEnergy">0</span> kcal/kg</td>
                            </tr>
                            <tr>
                                <td><strong>متوسط الألياف:</strong></td>
                                <td><span id="avgFiber">0</span>%</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <!-- تفاصيل المكونات -->
                <div class="mt-4">
                    <h6 class="text-info">تفاصيل المكونات:</h6>
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <thead class="table-secondary">
                                <tr>
                                    <th>المكون</th>
                                    <th>الكمية (كغ)</th>
                                    <th>التكلفة (د.أ)</th>
                                    <th>مساهمة البروتين (كغ)</th>
                                    <th>مساهمة الطاقة (kcal)</th>
                                    <th>مساهمة الألياف (كغ)</th>
                                </tr>
                            </thead>
                            <tbody id="detailsTable">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة حفظ الخلطة -->
<div class="modal fade" id="saveMixModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">حفظ الخلطة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="mixName" class="form-label">اسم الخلطة</label>
                    <input type="text" class="form-control" id="mixName" placeholder="أدخل اسم الخلطة" required>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" id="confirmSaveBtn">حفظ الخلطة</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentCalculations = null;
let mixtureIngredients = []; // لحفظ المكونات المضافة للخلطة

document.addEventListener('DOMContentLoaded', function() {
    const ingredientSelect = document.getElementById('ingredientSelect');
    const quantityInput = document.getElementById('quantityInput');
    const addIngredientBtn = document.getElementById('addIngredientBtn');
    const clearBtn = document.getElementById('clearBtn');
    const calculateBtn = document.getElementById('calculateBtn');

    // تحقق من وجود خلطة مقترحة في Local Storage
    checkForSuggestedMix();
    const saveBtn = document.getElementById('saveBtn');
    const saveMixModal = new bootstrap.Modal(document.getElementById('saveMixModal'));
    
    // مستمعات الأحداث
    ingredientSelect.addEventListener('change', onIngredientSelect);
    quantityInput.addEventListener('input', checkAddButtonState);
    addIngredientBtn.addEventListener('click', addIngredientToMix);
    clearBtn.addEventListener('click', clearMixture);
    calculateBtn.addEventListener('click', calculateMix);
    saveBtn.addEventListener('click', () => saveMixModal.show());
    document.getElementById('confirmSaveBtn').addEventListener('click', saveMix);
    
    // تفعيل الضغط على Enter لإضافة المكون
    quantityInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !addIngredientBtn.disabled) {
            addIngredientToMix();
        }
    });
});

function onIngredientSelect() {
    const select = document.getElementById('ingredientSelect');
    const selectedOption = select.options[select.selectedIndex];
    
    if (selectedOption.value) {
        // عرض معلومات المكون المختار
        document.getElementById('selectedProtein').textContent = selectedOption.dataset.protein + '%';
        document.getElementById('selectedEnergy').textContent = selectedOption.dataset.energy + ' kcal/kg';
        document.getElementById('selectedFiber').textContent = selectedOption.dataset.fiber + '%';
        document.getElementById('selectedPrice').textContent = parseFloat(selectedOption.dataset.price).toFixed(3) + ' د.أ';
        
        // تركيز على حقل الكمية
        document.getElementById('quantityInput').focus();
    } else {
        // مسح المعلومات
        document.getElementById('selectedProtein').textContent = '-';
        document.getElementById('selectedEnergy').textContent = '-';
        document.getElementById('selectedFiber').textContent = '-';
        document.getElementById('selectedPrice').textContent = '-';
    }
    
    checkAddButtonState();
}

function checkAddButtonState() {
    const select = document.getElementById('ingredientSelect');
    const quantity = parseFloat(document.getElementById('quantityInput').value) || 0;
    const addBtn = document.getElementById('addIngredientBtn');
    
    // التحقق من وجود المكون مسبقاً
    const ingredientExists = mixtureIngredients.some(item => item.id === parseInt(select.value));
    
    if (select.value && quantity > 0 && !ingredientExists) {
        addBtn.disabled = false;
        addBtn.textContent = 'إضافة للخلطة';
        addBtn.className = 'btn btn-primary';
    } else if (ingredientExists) {
        addBtn.disabled = true;
        addBtn.textContent = 'المكون موجود مسبقاً';
        addBtn.className = 'btn btn-warning';
    } else {
        addBtn.disabled = true;
        addBtn.textContent = 'إضافة للخلطة';
        addBtn.className = 'btn btn-primary';
    }
}

function addIngredientToMix() {
    const select = document.getElementById('ingredientSelect');
    const quantity = parseFloat(document.getElementById('quantityInput').value);
    const selectedOption = select.options[select.selectedIndex];
    
    if (!select.value || quantity <= 0) return;
    
    // إضافة المكون للقائمة
    const ingredient = {
        id: parseInt(select.value),
        name: selectedOption.text,
        protein: parseFloat(selectedOption.dataset.protein),
        energy: parseFloat(selectedOption.dataset.energy),
        fiber: parseFloat(selectedOption.dataset.fiber),
        price: parseFloat(selectedOption.dataset.price),
        quantity: quantity
    };
    
    mixtureIngredients.push(ingredient);
    
    // تحديث الجدول
    updateMixtureTable();
    
    // تحديث المعاينة السريعة
    updateQuickPreview();
    
    // مسح الاختيار
    select.value = '';
    document.getElementById('quantityInput').value = '';
    onIngredientSelect();
    
    // تفعيل زر الحساب
    document.getElementById('calculateBtn').disabled = false;
    
    // إخفاء الصف الفارغ
    document.getElementById('emptyRow').style.display = 'none';
}

function updateMixtureTable() {
    const tbody = document.getElementById('mixtureTableBody');
    const emptyRow = document.getElementById('emptyRow');
    
    // إزالة الصفوف الموجودة (عدا الصف الفارغ)
    const existingRows = tbody.querySelectorAll('tr:not(#emptyRow)');
    existingRows.forEach(row => row.remove());
    
    // إضافة الصفوف الجديدة
    mixtureIngredients.forEach((ingredient, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <strong>${ingredient.name}</strong>
                <span class="badge bg-secondary ms-2">${getCategoryBadge(ingredient.name)}</span>
            </td>
            <td class="text-center protein-color">${ingredient.protein}%</td>
            <td class="text-center energy-color">${ingredient.energy}</td>
            <td class="text-center fiber-color">${ingredient.fiber}%</td>
            <td class="text-center cost-color">${ingredient.price.toFixed(3)}</td>
            <td class="text-center">
                <input type="number" step="0.1" min="0.1" class="form-control form-control-sm text-center" 
                       value="${ingredient.quantity}" onchange="updateIngredientQuantity(${index}, this.value)">
            </td>
            <td class="text-center">
                <button type="button" class="btn btn-sm btn-outline-danger" 
                        onclick="removeIngredient(${index})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.insertBefore(row, emptyRow);
    });
    
    // تحديث العداد
    document.getElementById('mixtureCount').textContent = mixtureIngredients.length;
    
    // إظهار/إخفاء الصف الفارغ
    emptyRow.style.display = mixtureIngredients.length === 0 ? '' : 'none';
}

function getCategoryBadge(name) {
    const lowerName = name.toLowerCase();
    if (lowerName.includes('سيلاج')) return 'سيلاج';
    if (lowerName.includes('كسبة')) return 'كسب';
    if (lowerName.includes('ذرة') || lowerName.includes('شعير') || lowerName.includes('قمح')) return 'حبوب';
    if (lowerName.includes('زيت') || lowerName.includes('دهن')) return 'دهون';
    if (lowerName.includes('ملح') || lowerName.includes('كربونات')) return 'معادن';
    return 'أخرى';
}

function updateIngredientQuantity(index, newQuantity) {
    const quantity = parseFloat(newQuantity);
    if (quantity > 0) {
        mixtureIngredients[index].quantity = quantity;
        updateQuickPreview();
    }
}

function removeIngredient(index) {
    mixtureIngredients.splice(index, 1);
    updateMixtureTable();
    updateQuickPreview();
    checkAddButtonState();
    
    // تعطيل أزرار الحساب والحفظ إذا لم تبق مكونات
    if (mixtureIngredients.length === 0) {
        document.getElementById('calculateBtn').disabled = true;
        document.getElementById('saveBtn').disabled = true;
        document.getElementById('quickPreview').style.display = 'none';
    }
}

function clearMixture() {
    if (mixtureIngredients.length === 0) return;
    
    if (confirm('هل أنت متأكد من مسح جميع المكونات؟')) {
        mixtureIngredients = [];
        updateMixtureTable();
        document.getElementById('calculateBtn').disabled = true;
        document.getElementById('saveBtn').disabled = true;
        document.getElementById('quickPreview').style.display = 'none';
        document.getElementById('resultsSection').style.display = 'none';
        currentCalculations = null;
    }
}

function updateQuickPreview() {
    if (mixtureIngredients.length === 0) {
        document.getElementById('quickPreview').style.display = 'none';
        return;
    }
    
    let totalWeight = 0;
    let totalCost = 0;
    
    mixtureIngredients.forEach(ingredient => {
        totalWeight += ingredient.quantity;
        totalCost += ingredient.quantity * ingredient.price;
    });
    
    document.getElementById('previewWeight').textContent = totalWeight.toFixed(2);
    document.getElementById('previewCost').textContent = totalCost.toFixed(3);
    document.getElementById('previewCostPerKg').textContent = totalWeight > 0 ? (totalCost / totalWeight).toFixed(3) : '0';
    
    document.getElementById('quickPreview').style.display = 'block';
}

function calculateMix() {
    if (mixtureIngredients.length === 0) {
        alert('يرجى إضافة مكونات للخلطة أولاً');
        return;
    }
    
    const ingredients = mixtureIngredients.map(ingredient => ({
        id: ingredient.id,
        quantity: ingredient.quantity
    }));
    
    fetch('/calculate_mix', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            ingredients: ingredients
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('خطأ: ' + data.error);
            return;
        }
        
        currentCalculations = data;
        displayResults(data);
        document.getElementById('saveBtn').disabled = false;
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الحساب');
    });
}

function displayResults(data) {
    // عرض الملخص
    document.getElementById('totalWeight').textContent = data.total_weight;
    document.getElementById('totalCost').textContent = data.total_cost;
    document.getElementById('costPerKg').textContent = data.cost_per_kg;
    document.getElementById('avgProtein').textContent = data.avg_protein;
    document.getElementById('avgEnergy').textContent = data.avg_energy;
    document.getElementById('avgFiber').textContent = data.avg_fiber;
    
    // عرض التفاصيل
    const detailsTable = document.getElementById('detailsTable');
    detailsTable.innerHTML = '';
    
    data.details.forEach(detail => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${detail.name}</td>
            <td>${detail.quantity}</td>
            <td>${detail.cost}</td>
            <td>${detail.protein_contribution}</td>
            <td>${detail.energy_contribution}</td>
            <td>${detail.fiber_contribution}</td>
        `;
        detailsTable.appendChild(row);
    });
    
    // إظهار قسم النتائج
    document.getElementById('resultsSection').style.display = 'block';
    document.getElementById('resultsSection').scrollIntoView({ behavior: 'smooth' });
}

function saveMix() {
    const mixName = document.getElementById('mixName').value.trim();
    if (!mixName) {
        alert('يرجى إدخال اسم الخلطة');
        return;
    }
    
    if (!currentCalculations) {
        alert('يرجى حساب الخلطة أولاً');
        return;
    }
    
    const ingredients = mixtureIngredients.map(ingredient => ({
        id: ingredient.id,
        quantity: ingredient.quantity
    }));
    
    fetch('/save_mix', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            name: mixName,
            ingredients: ingredients,
            calculations: currentCalculations
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('خطأ: ' + data.error);
            return;
        }
        
        alert(data.message);
        bootstrap.Modal.getInstance(document.getElementById('saveMixModal')).hide();
        document.getElementById('mixName').value = '';
        
        // إعادة تعيين النموذج
        clearMixture();
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في حفظ الخلطة');
    });
}

function checkForSuggestedMix() {
    console.log('🔍 فحص وجود خلطة مقترحة...');

    const suggestedMixData = localStorage.getItem('suggestedMixData');

    if (suggestedMixData) {
        console.log('✅ تم العثور على خلطة مقترحة!');

        try {
            const mixData = JSON.parse(suggestedMixData);
            console.log('📊 بيانات الخلطة المقترحة:', mixData);

            // مسح البيانات من Local Storage
            localStorage.removeItem('suggestedMixData');

            // تحميل الخلطة المقترحة
            loadSuggestedMix(mixData);

        } catch (error) {
            console.error('❌ خطأ في قراءة بيانات الخلطة:', error);
            localStorage.removeItem('suggestedMixData');
        }
    } else {
        console.log('ℹ️ لا توجد خلطة مقترحة');
    }
}

function loadSuggestedMix(mixData) {
    console.log('📥 تحميل الخلطة المقترحة...');

    // مسح الخلطة الحالية
    clearMixture();

    // إضافة مكونات الخلطة المقترحة
    if (mixData.components && Array.isArray(mixData.components)) {
        mixData.components.forEach(component => {
            const ingredient = component.ingredient;
            const quantity = component.quantity;

            console.log(`➕ إضافة مكون: ${ingredient.name} - ${quantity} كغ`);

            // إضافة المكون للخلطة
            const newIngredient = {
                id: ingredient.id || Date.now(), // استخدام ID أو timestamp
                name: ingredient.name,
                protein: ingredient.protein,
                energy: ingredient.energy,
                fiber: ingredient.fiber || 0,
                price: ingredient.price_per_kg, // تحويل price_per_kg إلى price
                quantity: quantity
            };

            mixtureIngredients.push(newIngredient);
        });

        // تحديث عرض الخلطة
        updateMixtureTable();

        // تحديث المعاينة السريعة
        updateQuickPreview();

        // تفعيل أزرار الحساب والحفظ
        document.getElementById('calculateBtn').disabled = false;
        document.getElementById('saveBtn').disabled = false;

        // إظهار رسالة نجاح مفصلة
        const componentsList = mixData.components.map(comp =>
            `• ${comp.ingredient.name}: ${comp.quantity} كغ`
        ).join('\n');

        alert(`✅ تم تحميل الخلطة المقترحة بنجاح!\n\n📋 مكونات الخلطة:\n${componentsList}\n\n💡 يمكنك الآن تعديل الكميات أو إضافة مكونات جديدة ثم حفظ الخلطة.`);

        console.log('✅ تم تحميل الخلطة المقترحة بنجاح!');
    } else {
        console.error('❌ بيانات الخلطة غير صحيحة');
        alert('خطأ في بيانات الخلطة المقترحة');
    }
}
</script>
{% endblock %}