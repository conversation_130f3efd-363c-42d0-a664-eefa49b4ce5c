#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار سريع للتأكد من عمل المسارات
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_endpoints():
    """اختبار المسارات"""
    print("🔧 اختبار المسارات...")
    
    try:
        import requests
        
        # اختبار المسارات
        endpoints = [
            'http://localhost:5000/',
            'http://localhost:5000/compare_cows',
            'http://localhost:5000/cow_history',
            'http://localhost:5000/saved_mixes'
        ]
        
        for url in endpoints:
            try:
                response = requests.get(url, timeout=5)
                print(f"✅ {url}: {response.status_code}")
            except requests.exceptions.ConnectionError:
                print(f"❌ {url}: لا يمكن الاتصال (الخادم متوقف؟)")
            except Exception as e:
                print(f"⚠️ {url}: {str(e)}")
                
    except ImportError:
        print("⚠️ مكتبة requests غير متوفرة")
        print("يمكنك اختبار المسارات يدوياً في المتصفح:")
        print("- http://localhost:5000/compare_cows")
        print("- http://localhost:5000/cow_history")
        print("- http://localhost:5000/saved_mixes")

if __name__ == "__main__":
    test_endpoints()