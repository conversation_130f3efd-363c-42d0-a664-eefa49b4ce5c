{% extends "base.html" %}

{% block title %}مقارنة الأبقار - برنامج تكوين الأعلاف المركزة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-balance-scale me-2"></i>
                        مقارنة أداء الأبقار
                    </h5>
                </div>
                <div class="card-body">
                    <p>قارن بين أداء أبقارك المختلفة لتحديد الأكثر كفاءة في استهلاك العلف وإنتاج الحليب</p>
                </div>
            </div>
        </div>
    </div>

    {% if calculations and calculations|length > 0 %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-cow me-2"></i>
                        الأبقار المتاحة للمقارنة ({{ calculations|length }} بقرة)
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>اسم البقرة</th>
                                    <th>الوزن (كغ)</th>
                                    <th>إنتاج الحليب (ل/يوم)</th>
                                    <th>مرحلة الإنتاج</th>
                                    <th>مستوى النشاط</th>
                                    <th>التكلفة اليومية (د.أ)</th>
                                    <th>تاريخ الحساب</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for calc in calculations %}
                                <tr>
                                    <td><strong class="text-primary">{{ calc.cow_name }}</strong></td>
                                    <td class="text-center">{{ calc.weight }}</td>
                                    <td class="text-center">
                                        <span class="badge bg-info">{{ calc.milk_production }}</span>
                                    </td>
                                    <td class="text-center">
                                        {% if calc.lactation_stage == 'early' %}
                                            <span class="badge bg-success">مبكرة</span>
                                        {% elif calc.lactation_stage == 'mid' %}
                                            <span class="badge bg-warning">متوسطة</span>
                                        {% elif calc.lactation_stage == 'late' %}
                                            <span class="badge bg-secondary">متأخرة</span>
                                        {% else %}
                                            <span class="badge bg-light text-dark">{{ calc.lactation_stage }}</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        {% if calc.activity_level == 'low' %}
                                            <span class="badge bg-light text-dark">منخفض</span>
                                        {% elif calc.activity_level == 'moderate' %}
                                            <span class="badge bg-info">متوسط</span>
                                        {% elif calc.activity_level == 'high' %}
                                            <span class="badge bg-danger">عالي</span>
                                        {% else %}
                                            <span class="badge bg-light text-dark">{{ calc.activity_level or 'غير محدد' }}</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        <span class="text-success fw-bold">{{ "%.2f"|format(calc.daily_cost or 0) }}</span>
                                    </td>
                                    <td class="text-center">
                                        {% if calc.calculation_date %}
                                            <small class="text-muted">{{ calc.calculation_date.strftime('%Y-%m-%d') }}</small>
                                        {% else %}
                                            <small class="text-muted">غير محدد</small>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات بسيطة -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-primary">{{ calculations|length }}</h4>
                    <p class="card-text">إجمالي الأبقار</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-success">{{ "%.1f"|format((calculations|sum(attribute='milk_production')) / (calculations|length)) }}</h4>
                    <p class="card-text">متوسط إنتاج الحليب (ل/يوم)</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-warning">{{ "%.2f"|format((calculations|sum(attribute='daily_cost')) / (calculations|length)) }}</h4>
                    <p class="card-text">متوسط التكلفة اليومية (د.أ)</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-info">{{ "%.2f"|format(calculations|sum(attribute='daily_cost')) }}</h4>
                    <p class="card-text">إجمالي التكلفة اليومية (د.أ)</p>
                </div>
            </div>
        </div>
    </div>

    {% else %}
    <!-- لا توجد بيانات -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-cow fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد بيانات أبقار للمقارنة</h4>
                    <p class="text-muted">يجب حساب احتياجات بقرتين على الأقل لإجراء المقارنة</p>
                    <a href="{{ url_for('cow_requirements') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-plus me-2"></i>
                        ابدأ بحساب احتياجات البقرة الأولى
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}