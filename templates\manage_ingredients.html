{% extends "base.html" %}

{% block title %}إدارة المكونات - برنامج تكوين الأعلاف المركزة{% endblock %}

{% block content %}
<div class="row">
    <!-- قسم إضافة مكون جديد -->
    <div class="col-lg-5 mb-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-plus-circle me-2"></i>
                    إضافة مكون جديد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('add_ingredient') }}" class="needs-validation" novalidate>
                    <div class="mb-3">
                        <label for="name" class="form-label">
                            <i class="fas fa-tag me-1 text-primary"></i>
                            اسم المكون <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="name" name="name" 
                               placeholder="مثال: ذرة صفراء" required>
                        <div class="invalid-feedback">
                            يرجى إدخال اسم المكون
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="protein" class="form-label">
                                    <i class="fas fa-dna me-1 text-danger"></i>
                                    نسبة البروتين (%) <span class="text-danger">*</span>
                                </label>
                                <input type="number" step="0.1" min="0" max="100" 
                                       class="form-control" id="protein" name="protein" 
                                       placeholder="0.0" required>
                                <div class="invalid-feedback">
                                    يرجى إدخال نسبة البروتين (0-100%)
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="energy" class="form-label">
                                    <i class="fas fa-bolt me-1 text-warning"></i>
                                    الطاقة (kcal/kg) <span class="text-danger">*</span>
                                </label>
                                <input type="number" step="1" min="0" max="10000" 
                                       class="form-control" id="energy" name="energy" 
                                       placeholder="0" required>
                                <div class="invalid-feedback">
                                    يرجى إدخال قيمة الطاقة
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="fiber" class="form-label">
                                    <i class="fas fa-leaf me-1 text-success"></i>
                                    نسبة الألياف (%) <span class="text-danger">*</span>
                                </label>
                                <input type="number" step="0.1" min="0" max="100" 
                                       class="form-control" id="fiber" name="fiber" 
                                       placeholder="0.0" required>
                                <div class="invalid-feedback">
                                    يرجى إدخال نسبة الألياف (0-100%)
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="price_per_kg" class="form-label">
                                    <i class="fas fa-money-bill-wave me-1 text-info"></i>
                                    السعر/كغ (د.أ) <span class="text-danger">*</span>
                                </label>
                                <input type="number" step="0.001" min="0" max="1000" 
                                       class="form-control" id="price_per_kg" name="price_per_kg" 
                                       placeholder="0.000" required>
                                <div class="invalid-feedback">
                                    يرجى إدخال السعر بالدينار الأردني
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-plus me-2"></i>
                            إضافة المكون
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- قسم المعلومات المساعدة -->
        <div class="card mt-4">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات مساعدة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-12">
                        <h6 class="text-primary">أمثلة على القيم الغذائية:</h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead>
                                    <tr>
                                        <th>نوع المكون</th>
                                        <th>البروتين %</th>
                                        <th>الطاقة</th>
                                        <th>الألياف %</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>حبوب (ذرة، شعير)</td>
                                        <td>8-12</td>
                                        <td>2800-3400</td>
                                        <td>2-6</td>
                                    </tr>
                                    <tr>
                                        <td>كسب بروتيني</td>
                                        <td>28-65</td>
                                        <td>1800-2800</td>
                                        <td>3-30</td>
                                    </tr>
                                    <tr>
                                        <td>سيلاج</td>
                                        <td>8-20</td>
                                        <td>1200-2200</td>
                                        <td>25-35</td>
                                    </tr>
                                    <tr>
                                        <td>مخلفات صناعية</td>
                                        <td>3-18</td>
                                        <td>1200-3200</td>
                                        <td>8-45</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قسم عرض المكونات الموجودة -->
    <div class="col-lg-7 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        المكونات المتوفرة ({{ ingredients|length }} مكون)
                    </h5>
                    <div class="input-group" style="width: 300px;">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="searchInput" 
                               placeholder="البحث في المكونات...">
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="ingredientsTable">
                        <thead class="table-dark">
                            <tr>
                                <th style="width: 25%">المكون</th>
                                <th style="width: 15%" class="text-center">البروتين %</th>
                                <th style="width: 15%" class="text-center">الطاقة</th>
                                <th style="width: 15%" class="text-center">الألياف %</th>
                                <th style="width: 15%" class="text-center">السعر/كغ</th>
                                <th style="width: 15%" class="text-center">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for ingredient in ingredients %}
                            <tr class="ingredient-row">
                                <td>
                                    <strong class="ingredient-name">{{ ingredient.name }}</strong>
                                    {% if 'سيلاج' in ingredient.name %}
                                        <span class="badge bg-success ms-2">سيلاج</span>
                                    {% elif 'كسبة' in ingredient.name %}
                                        <span class="badge bg-warning ms-2">كسب</span>
                                    {% elif 'ذرة' in ingredient.name or 'شعير' in ingredient.name or 'قمح' in ingredient.name %}
                                        <span class="badge bg-primary ms-2">حبوب</span>
                                    {% elif 'زيت' in ingredient.name or 'دهن' in ingredient.name %}
                                        <span class="badge bg-info ms-2">دهون</span>
                                    {% elif 'ملح' in ingredient.name or 'كربونات' in ingredient.name %}
                                        <span class="badge bg-secondary ms-2">معادن</span>
                                    {% else %}
                                        <span class="badge bg-dark ms-2">أخرى</span>
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    <span class="protein-color">{{ ingredient.protein }}</span>
                                </td>
                                <td class="text-center">
                                    <span class="energy-color">{{ ingredient.energy }}</span>
                                </td>
                                <td class="text-center">
                                    <span class="fiber-color">{{ ingredient.fiber }}</span>
                                </td>
                                <td class="text-center">
                                    <span class="cost-color">{{ "%.3f"|format(ingredient.price_per_kg) }}</span>
                                </td>
                                <td class="text-center">
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary" 
                                                onclick="editIngredient({{ ingredient.id }}, '{{ ingredient.name }}', {{ ingredient.protein }}, {{ ingredient.energy }}, {{ ingredient.fiber }}, {{ ingredient.price_per_kg }})"
                                                data-bs-toggle="tooltip" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deleteIngredient({{ ingredient.id }}, '{{ ingredient.name }}')"
                                                data-bs-toggle="tooltip" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-seedling fa-2x mb-2"></i>
                        <h4 id="totalIngredients">{{ ingredients|length }}</h4>
                        <small>إجمالي المكونات</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-leaf fa-2x mb-2"></i>
                        <h4 id="silagCount">
                            {% set silage_count = 0 %}
                            {% for ingredient in ingredients %}
                                {% if 'سيلاج' in ingredient.name %}
                                    {% set silage_count = silage_count + 1 %}
                                {% endif %}
                            {% endfor %}
                            {{ silage_count }}
                        </h4>
                        <small>أعلاف سيلاج</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-circle fa-2x mb-2"></i>
                        <h4 id="grainCount">
                            {% set grain_count = 0 %}
                            {% for ingredient in ingredients %}
                                {% if 'ذرة' in ingredient.name or 'شعير' in ingredient.name or 'قمح' in ingredient.name %}
                                    {% set grain_count = grain_count + 1 %}
                                {% endif %}
                            {% endfor %}
                            {{ grain_count }}
                        </h4>
                        <small>حبوب</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-coins fa-2x mb-2"></i>
                        <h4 id="avgPrice">
                            {% set total_price = 0 %}
                            {% for ingredient in ingredients %}
                                {% set total_price = total_price + ingredient.price_per_kg %}
                            {% endfor %}
                            {{ "%.2f"|format(total_price / ingredients|length if ingredients|length > 0 else 0) }}
                        </h4>
                        <small>متوسط السعر</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تعديل المكون -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">تعديل المكون</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="editForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" id="editId" name="id">
                    
                    <div class="mb-3">
                        <label for="editName" class="form-label">اسم المكون</label>
                        <input type="text" class="form-control" id="editName" name="name" required>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editProtein" class="form-label">نسبة البروتين (%)</label>
                                <input type="number" step="0.1" class="form-control" id="editProtein" name="protein" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editEnergy" class="form-label">الطاقة (kcal/kg)</label>
                                <input type="number" step="1" class="form-control" id="editEnergy" name="energy" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editFiber" class="form-label">نسبة الألياف (%)</label>
                                <input type="number" step="0.1" class="form-control" id="editFiber" name="fiber" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editPrice" class="form-label">السعر/كغ (د.أ)</label>
                                <input type="number" step="0.001" class="form-control" id="editPrice" name="price_per_kg" required>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف المكون: <strong id="deleteIngredientName"></strong>؟</p>
                <p class="text-danger">تحذير: سيؤثر هذا على جميع الخلطات التي تحتوي على هذا المكون.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف المكون</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let editModal = null;
let deleteModal = null;

document.addEventListener('DOMContentLoaded', function() {
    editModal = new bootstrap.Modal(document.getElementById('editModal'));
    deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    
    // تفعيل البحث
    setupSearch();
    
    // تفعيل التحقق من صحة النماذج
    setupFormValidation();
});

function setupSearch() {
    const searchInput = document.getElementById('searchInput');
    const rows = document.querySelectorAll('.ingredient-row');
    
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        
        rows.forEach(row => {
            const name = row.querySelector('.ingredient-name').textContent.toLowerCase();
            if (name.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });
}

function setupFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
}

function editIngredient(id, name, protein, energy, fiber, price) {
    document.getElementById('editId').value = id;
    document.getElementById('editName').value = name;
    document.getElementById('editProtein').value = protein;
    document.getElementById('editEnergy').value = energy;
    document.getElementById('editFiber').value = fiber;
    document.getElementById('editPrice').value = price;
    
    document.getElementById('editForm').action = `/edit_ingredient/${id}`;
    editModal.show();
}

function deleteIngredient(id, name) {
    document.getElementById('deleteIngredientName').textContent = name;
    document.getElementById('deleteForm').action = `/delete_ingredient/${id}`;
    deleteModal.show();
}

// تأثيرات بصرية للصفوف
document.addEventListener('DOMContentLoaded', function() {
    const rows = document.querySelectorAll('.ingredient-row');
    rows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
            this.style.transform = 'scale(1.01)';
            this.style.transition = 'all 0.2s ease';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
            this.style.transform = 'scale(1)';
        });
    });
});
</script>
{% endblock %}