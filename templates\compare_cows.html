{% extends "base.html" %}

{% block title %}مقارنة الأبقار - برنامج تكوين الأعلاف المركزة{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-balance-scale me-2"></i>
                    مقارنة أداء الأبقار
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <p class="mb-0">قارن بين أداء أبقارك المختلفة لتحديد الأكثر كفاءة في استهلاك العلف وإنتاج الحليب</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="{{ url_for('cow_requirements') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>
                            حساب بقرة جديدة
                        </a>
                        <a href="{{ url_for('cow_history') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-history me-1"></i>
                            عرض التاريخ
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% if calculations %}
<div class="row">
    <!-- قسم اختيار الأبقار للمقارنة -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0">
                    <i class="fas fa-check-square me-2"></i>
                    اختيار الأبقار للمقارنة
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="searchCows" placeholder="البحث في الأبقار...">
                </div>
                
                <div class="mb-3">
                    <label class="form-label">عدد الأبقار المختارة: <span id="selectedCount" class="badge bg-primary">0</span></label>
                </div>
                
                <div style="max-height: 400px; overflow-y: auto;">
                    {% for calc in calculations %}
                    <div class="form-check mb-2 cow-option" data-name="{{ calc.cow_name|lower }}">
                        <input class="form-check-input cow-checkbox" type="checkbox" 
                               value="{{ calc.id }}" id="cow{{ calc.id }}"
                               data-name="{{ calc.cow_name }}"
                               data-weight="{{ calc.weight }}"
                               data-milk="{{ calc.milk_production }}"
                               data-dm="{{ calc.total_dm }}"
                               data-protein="{{ calc.total_protein }}"
                               data-energy="{{ calc.total_energy }}"
                               data-cost="{{ calc.daily_cost }}"
                               data-stage="{{ calc.lactation_stage }}"
                               data-date="{{ calc.calculation_date.strftime('%Y-%m-%d') if calc.calculation_date else '' }}">
                        <label class="form-check-label w-100" for="cow{{ calc.id }}">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>{{ calc.cow_name }}</strong>
                                    <br><small class="text-muted">
                                        {{ calc.weight }}كغ - {{ calc.milk_production }}ل/يوم
                                    </small>
                                </div>
                                <div class="text-end">
                                    <small class="text-success">{{ "%.2f"|format(calc.daily_cost or 0) }} د.أ</small>
                                    <br><small class="text-muted">{{ calc.calculation_date.strftime('%Y-%m-%d') if calc.calculation_date else '' }}</small>
                                </div>
                            </div>
                        </label>
                    </div>
                    {% endfor %}
                </div>
                
                <div class="mt-3">
                    <button class="btn btn-success w-100" id="compareBtn" disabled>
                        <i class="fas fa-chart-bar me-2"></i>
                        مقارنة الأبقار المختارة
                    </button>
                </div>
                
                <div class="mt-2">
                    <div class="btn-group w-100">
                        <button class="btn btn-outline-secondary btn-sm" onclick="selectAll()">
                            <i class="fas fa-check-double me-1"></i>
                            اختيار الكل
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                            <i class="fas fa-times me-1"></i>
                            مسح الاختيار
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قسم نتائج المقارنة -->
    <div class="col-lg-8 mb-4">
        <!-- رسالة قبل المقارنة -->
        <div class="card" id="beforeComparisonCard">
            <div class="card-body text-center py-5">
                <i class="fas fa-balance-scale fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">اختر على الأقل بقرتين للمقارنة</h4>
                <p class="text-muted">حدد الأبقار من القائمة على اليسار واضغط "مقارنة"</p>
            </div>
        </div>

        <!-- نتائج المقارنة -->
        <div id="comparisonResults" style="display: none;">
            <!-- جدول المقارنة -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        جدول المقارنة التفصيلي
                    </h6>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="comparisonTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>اسم البقرة</th>
                                    <th class="text-center">الوزن (كغ)</th>
                                    <th class="text-center">الحليب (ل/يوم)</th>
                                    <th class="text-center">العلف (كغ/يوم)</th>
                                    <th class="text-center">البروتين (كغ)</th>
                                    <th class="text-center">الطاقة (Mcal)</th>
                                    <th class="text-center">التكلفة (د.أ/يوم)</th>
                                    <th class="text-center">كفاءة التحويل</th>
                                    <th class="text-center">التكلفة/لتر</th>
                                </tr>
                            </thead>
                            <tbody id="comparisonTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- تحليل الكفاءة -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-trophy me-2"></i>
                        تصنيف الكفاءة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-success">🏆 الأكثر كفاءة في استهلاك العلف:</h6>
                            <div id="mostEfficientFeed" class="alert alert-success mb-3">
                            </div>
                            
                            <h6 class="text-primary">💰 الأقل تكلفة للتر الواحد:</h6>
                            <div id="mostCostEffective" class="alert alert-primary mb-3">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-warning">🥛 الأعلى إنتاجاً للحليب:</h6>
                            <div id="highestMilk" class="alert alert-warning mb-3">
                            </div>
                            
                            <h6 class="text-danger">⚠️ تحتاج تحسين:</h6>
                            <div id="needsImprovement" class="alert alert-danger mb-3">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- رسوم بيانية -->
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-secondary text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-bar me-2"></i>
                                مقارنة التكلفة اليومية
                            </h6>
                        </div>
                        <div class="card-body">
                            <canvas id="costChart" width="400" height="300"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-secondary text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                كفاءة التحويل الغذائي
                            </h6>
                        </div>
                        <div class="card-body">
                            <canvas id="efficiencyChart" width="400" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات شاملة -->
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>
                        إحصائيات المجموعة المختارة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <h4 id="avgMilkProduction" class="text-primary">0</h4>
                                <small>متوسط إنتاج الحليب (لتر/يوم)</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <h4 id="avgDailyCost" class="text-success">0</h4>
                                <small>متوسط التكلفة اليومية (د.أ)</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <h4 id="avgFeedEfficiency" class="text-warning">0</h4>
                                <small>متوسط كفاءة التحويل</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <h4 id="totalDailyCost" class="text-danger">0</h4>
                                <small>إجمالي التكلفة اليومية (د.أ)</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% else %}
<!-- لا توجد بيانات -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-cow fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد بيانات أبقار للمقارنة</h4>
                <p class="text-muted">يجب حساب احتياجات بقرتين على الأقل لإجراء المقارنة</p>
                <a href="{{ url_for('cow_requirements') }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus me-2"></i>
                    ابدأ بحساب احتياجات البقرة الأولى
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
let selectedCows = [];

document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
});

function setupEventListeners() {
    // البحث في الأبقار
    const searchInput = document.getElementById('searchCows');
    if (searchInput) {
        searchInput.addEventListener('input', searchCows);
    }
    
    // اختيار الأبقار
    const checkboxes = document.querySelectorAll('.cow-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelection);
    });
    
    // زر المقارنة
    const compareBtn = document.getElementById('compareBtn');
    if (compareBtn) {
        compareBtn.addEventListener('click', compareCows);
    }
}

function searchCows() {
    const searchTerm = document.getElementById('searchCows').value.toLowerCase();
    const cowOptions = document.querySelectorAll('.cow-option');
    
    cowOptions.forEach(option => {
        const name = option.dataset.name;
        if (name.includes(searchTerm)) {
            option.style.display = 'block';
        } else {
            option.style.display = 'none';
        }
    });
}

function updateSelection() {
    const checkboxes = document.querySelectorAll('.cow-checkbox:checked');
    selectedCows = Array.from(checkboxes);
    
    document.getElementById('selectedCount').textContent = selectedCows.length;
    document.getElementById('compareBtn').disabled = selectedCows.length < 2;
}

function selectAll() {
    const checkboxes = document.querySelectorAll('.cow-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelection();
}

function clearSelection() {
    const checkboxes = document.querySelectorAll('.cow-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelection();
}

function compareCows() {
    if (selectedCows.length < 2) {
        alert('يجب اختيار بقرتين على الأقل للمقارنة');
        return;
    }
    
    // جمع بيانات الأبقار المختارة
    const cowsData = selectedCows.map(checkbox => ({
        id: checkbox.value,
        name: checkbox.dataset.name,
        weight: parseFloat(checkbox.dataset.weight),
        milk: parseFloat(checkbox.dataset.milk),
        dm: parseFloat(checkbox.dataset.dm),
        protein: parseFloat(checkbox.dataset.protein),
        energy: parseFloat(checkbox.dataset.energy),
        cost: parseFloat(checkbox.dataset.cost),
        stage: checkbox.dataset.stage,
        date: checkbox.dataset.date,
        feedEfficiency: parseFloat(checkbox.dataset.dm) / parseFloat(checkbox.dataset.milk),
        costPerLiter: parseFloat(checkbox.dataset.cost) / parseFloat(checkbox.dataset.milk)
    }));
    
    // عرض النتائج
    displayComparisonResults(cowsData);
    
    // إخفاء الرسالة وإظهار النتائج
    document.getElementById('beforeComparisonCard').style.display = 'none';
    document.getElementById('comparisonResults').style.display = 'block';
    
    // التمرير للنتائج
    document.getElementById('comparisonResults').scrollIntoView({ behavior: 'smooth' });
}

function displayComparisonResults(cowsData) {
    // ملء جدول المقارنة
    fillComparisonTable(cowsData);
    
    // تحليل الكفاءة
    analyzeEfficiency(cowsData);
    
    // رسم المخططات
    drawCharts(cowsData);
    
    // حساب الإحصائيات
    calculateGroupStatistics(cowsData);
}

function fillComparisonTable(cowsData) {
    const tableBody = document.getElementById('comparisonTableBody');
    tableBody.innerHTML = '';
    
    cowsData.forEach(cow => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><strong class="text-primary">${cow.name}</strong></td>
            <td class="text-center">${cow.weight}</td>
            <td class="text-center"><span class="badge bg-info">${cow.milk}</span></td>
            <td class="text-center">${cow.dm.toFixed(1)}</td>
            <td class="text-center">${(cow.protein / 1000).toFixed(2)}</td>
            <td class="text-center">${cow.energy.toFixed(1)}</td>
            <td class="text-center"><span class="text-success fw-bold">${cow.cost.toFixed(2)}</span></td>
            <td class="text-center">${cow.feedEfficiency.toFixed(2)}</td>
            <td class="text-center">${cow.costPerLiter.toFixed(3)}</td>
        `;
        tableBody.appendChild(row);
    });
}

function analyzeEfficiency(cowsData) {
    // الأكثر كفاءة في استهلاك العلف (أقل feed efficiency)
    const mostEfficientFeed = cowsData.reduce((prev, current) => 
        prev.feedEfficiency < current.feedEfficiency ? prev : current
    );
    
    // الأقل تكلفة للتر
    const mostCostEffective = cowsData.reduce((prev, current) => 
        prev.costPerLiter < current.costPerLiter ? prev : current
    );
    
    // الأعلى إنتاجاً
    const highestMilk = cowsData.reduce((prev, current) => 
        prev.milk > current.milk ? prev : current
    );
    
    // تحتاج تحسين (أعلى feed efficiency)
    const needsImprovement = cowsData.reduce((prev, current) => 
        prev.feedEfficiency > current.feedEfficiency ? prev : current
    );
    
    // عرض النتائج
    document.getElementById('mostEfficientFeed').innerHTML = `
        <strong>${mostEfficientFeed.name}</strong><br>
        <small>كفاءة التحويل: ${mostEfficientFeed.feedEfficiency.toFixed(2)} كغ علف/لتر حليب</small>
    `;
    
    document.getElementById('mostCostEffective').innerHTML = `
        <strong>${mostCostEffective.name}</strong><br>
        <small>التكلفة: ${mostCostEffective.costPerLiter.toFixed(3)} د.أ/لتر</small>
    `;
    
    document.getElementById('highestMilk').innerHTML = `
        <strong>${highestMilk.name}</strong><br>
        <small>الإنتاج: ${highestMilk.milk} لتر/يوم</small>
    `;
    
    document.getElementById('needsImprovement').innerHTML = `
        <strong>${needsImprovement.name}</strong><br>
        <small>كفاءة التحويل: ${needsImprovement.feedEfficiency.toFixed(2)} (يحتاج تحسين)</small>
    `;
}

function drawCharts(cowsData) {
    // هنا يمكن إضافة مكتبة Chart.js لرسم المخططات
    // للآن سنعرض نص بسيط
    document.getElementById('costChart').style.display = 'none';
    document.getElementById('efficiencyChart').style.display = 'none';
    
    // إضافة نص بديل
    const costChartContainer = document.getElementById('costChart').parentNode;
    costChartContainer.innerHTML = `
        <h6 class="text-center">مقارنة التكلفة اليومية</h6>
        <div class="text-center">
            ${cowsData.map(cow => `
                <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                    <span>${cow.name}</span>
                    <span class="badge bg-primary">${cow.cost.toFixed(2)} د.أ</span>
                </div>
            `).join('')}
        </div>
    `;
    
    const efficiencyChartContainer = document.getElementById('efficiencyChart').parentNode;
    efficiencyChartContainer.innerHTML = `
        <h6 class="text-center">كفاءة التحويل الغذائي</h6>
        <div class="text-center">
            ${cowsData.map(cow => `
                <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                    <span>${cow.name}</span>
                    <span class="badge bg-success">${cow.feedEfficiency.toFixed(2)}</span>
                </div>
            `).join('')}
        </div>
    `;
}

function calculateGroupStatistics(cowsData) {
    const totalMilk = cowsData.reduce((sum, cow) => sum + cow.milk, 0);
    const totalCost = cowsData.reduce((sum, cow) => sum + cow.cost, 0);
    const totalEfficiency = cowsData.reduce((sum, cow) => sum + cow.feedEfficiency, 0);
    
    const avgMilk = totalMilk / cowsData.length;
    const avgCost = totalCost / cowsData.length;
    const avgEfficiency = totalEfficiency / cowsData.length;
    
    document.getElementById('avgMilkProduction').textContent = avgMilk.toFixed(1);
    document.getElementById('avgDailyCost').textContent = avgCost.toFixed(2);
    document.getElementById('avgFeedEfficiency').textContent = avgEfficiency.toFixed(2);
    document.getElementById('totalDailyCost').textContent = totalCost.toFixed(2);
}
</script>
{% endblock %}