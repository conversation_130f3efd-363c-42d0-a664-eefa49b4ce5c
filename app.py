from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import json

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///feed_mixer.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

# نماذج قاعدة البيانات
class Ingredient(db.Model):
    """جدول المكونات"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    protein = db.Column(db.Float, nullable=False)  # نسبة البروتين %
    energy = db.Column(db.Float, nullable=False)   # الطاقة kcal/kg
    fiber = db.Column(db.Float, nullable=False)    # نسبة الألياف %
    price_per_kg = db.Column(db.Float, nullable=False)  # السعر/كغ بالدينار الأردني
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'protein': self.protein,
            'energy': self.energy,
            'fiber': self.fiber,
            'price_per_kg': self.price_per_kg
        }

class Mix(db.Model):
    """جدول الخلطات"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    date_created = db.Column(db.DateTime, default=datetime.utcnow)
    total_weight = db.Column(db.Float, nullable=False)
    total_cost = db.Column(db.Float, nullable=False)
    avg_protein = db.Column(db.Float, nullable=False)
    avg_energy = db.Column(db.Float, nullable=False)
    avg_fiber = db.Column(db.Float, nullable=False)
    
    # العلاقة مع تفاصيل الخلطة
    details = db.relationship('MixDetail', backref='mix', lazy=True, cascade='all, delete-orphan')

class MixDetail(db.Model):
    """جدول تفاصيل الخلطة"""
    id = db.Column(db.Integer, primary_key=True)
    mix_id = db.Column(db.Integer, db.ForeignKey('mix.id'), nullable=False)
    ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredient.id'), nullable=False)
    quantity_kg = db.Column(db.Float, nullable=False)  # الكمية بالكيلوغرام
    
    # العلاقة مع المكون
    ingredient = db.relationship('Ingredient', backref='mix_details')

# إنشاء قاعدة البيانات
with app.app_context():
    db.create_all()
    
    # إضافة بيانات تجريبية إذا لم تكن موجودة
    if Ingredient.query.count() == 0:
        sample_ingredients = [
            Ingredient(name='ذرة صفراء', protein=8.5, energy=3350, fiber=2.3, price_per_kg=0.45),
            Ingredient(name='كسبة فول الصويا', protein=44.0, energy=2230, fiber=7.0, price_per_kg=0.65),
            Ingredient(name='شعير', protein=11.5, energy=2800, fiber=5.5, price_per_kg=0.38),
            Ingredient(name='نخالة قمح', protein=15.5, energy=1260, fiber=10.0, price_per_kg=0.25),
            Ingredient(name='كربونات كالسيوم', protein=0.0, energy=0, fiber=0.0, price_per_kg=0.15),
            Ingredient(name='ملح طعام', protein=0.0, energy=0, fiber=0.0, price_per_kg=0.20),
        ]
        
        for ingredient in sample_ingredients:
            db.session.add(ingredient)
        db.session.commit()

class CowCalculation(db.Model):
    """جدول حسابات الأبقار"""
    id = db.Column(db.Integer, primary_key=True)
    cow_name = db.Column(db.String(100), nullable=False, default='بقرة')
    weight = db.Column(db.Float, nullable=False)
    milk_production = db.Column(db.Float, nullable=False)
    lactation_stage = db.Column(db.String(50), nullable=False)
    activity_level = db.Column(db.String(50), nullable=False)
    calculation_date = db.Column(db.DateTime, default=datetime.utcnow)
    
    # الاحتياجات المحسوبة
    total_dm = db.Column(db.Float, nullable=False)
    total_protein = db.Column(db.Float, nullable=False)
    total_energy = db.Column(db.Float, nullable=False)
    
    # الخلطة المقترحة
    suggested_mix = db.Column(db.Text, nullable=True) # JSON string
    daily_cost = db.Column(db.Float, nullable=True)

# ... (الكود الحالي)

@app.route('/animal_requirements')
def animal_requirements():
    """صفحة حساب احتياجات جميع الحيوانات"""
    return render_template('animal_requirements.html', title="احتياجات الحيوانات")

@app.route('/calculate_animal_requirements', methods=['POST'])
def calculate_animal_requirements():
    """حساب احتياجات الحيوانات المختلفة"""
    try:
        data = request.get_json()
        animal_type = data.get('animal_type')

        if animal_type == 'cow':
            weight = float(data.get('cow_weight', 0))
            milk = float(data.get('cow_milk', 0))
            stage = data.get('cow_stage', 'mid')
            activity = data.get('activity_level', 'moderate')

            if not weight or weight < 350 or weight > 800:
                return jsonify({'error': 'وزن البقرة يجب أن يكون بين 350-800 كغ'})

            if milk < 0 or milk > 80:
                return jsonify({'error': 'إنتاج الحليب يجب أن يكون بين 0-80 لتر'})

            requirements = calculate_nutritional_requirements(weight, milk, stage, activity)
            return jsonify({'requirements': requirements})

        elif animal_type == 'calf':
            weight = float(data.get('calf_weight', 0))
            age = float(data.get('calf_age', 0))
            growth = float(data.get('calf_growth', 0))
            calf_type = data.get('calf_type', 'dairy')

            if not weight or weight < 50 or weight > 300:
                return jsonify({'error': 'وزن العجل يجب أن يكون بين 50-300 كغ'})

            if age < 2 or age > 24:
                return jsonify({'error': 'عمر العجل يجب أن يكون بين 2-24 شهر'})

            requirements = calculate_calf_requirements(weight, age, growth, calf_type)
            return jsonify({'requirements': requirements})

        elif animal_type == 'sheep':
            weight = float(data.get('sheep_weight', 0))
            stage = data.get('sheep_stage', 'maintenance')
            lambs = float(data.get('sheep_lambs', 0))

            if not weight or weight < 20 or weight > 120:
                return jsonify({'error': 'وزن الخروف يجب أن يكون بين 20-120 كغ'})

            requirements = calculate_sheep_requirements(weight, stage, lambs)
            return jsonify({'requirements': requirements})

        elif animal_type == 'goat':
            weight = float(data.get('goat_weight', 0))
            milk = float(data.get('goat_milk', 0))
            stage = data.get('goat_stage', 'maintenance')

            if not weight or weight < 15 or weight > 100:
                return jsonify({'error': 'وزن الماعز يجب أن يكون بين 15-100 كغ'})

            requirements = calculate_goat_requirements(weight, milk, stage)
            return jsonify({'requirements': requirements})

        elif animal_type == 'buffalo':
            weight = float(data.get('buffalo_weight', 0))
            milk = float(data.get('buffalo_milk', 0))
            stage = data.get('buffalo_stage', 'mid')
            activity = data.get('activity_level', 'moderate')

            if not weight or weight < 400 or weight > 900:
                return jsonify({'error': 'وزن الجاموس يجب أن يكون بين 400-900 كغ'})

            requirements = calculate_buffalo_requirements(weight, milk, stage, activity)
            return jsonify({'requirements': requirements})

        else:
            return jsonify({'error': 'نوع حيوان غير مدعوم'})

    except ValueError as e:
        return jsonify({'error': f'خطأ في البيانات المدخلة: {str(e)}'})
    except Exception as e:
        return jsonify({'error': f'خطأ في الحساب: {str(e)}'})

def calculate_nutritional_requirements(weight, milk, stage, activity):
    """حساب الاحتياجات الغذائية للبقرة حسب معادلات NRC"""

    # معاملات التصحيح حسب مرحلة الإنتاج
    stage_factors = {
        'early': {'dm': 1.1, 'protein': 1.15, 'energy': 1.1},
        'mid': {'dm': 1.0, 'protein': 1.0, 'energy': 1.0},
        'late': {'dm': 0.95, 'protein': 0.9, 'energy': 0.95},
        'dry': {'dm': 0.8, 'protein': 0.7, 'energy': 0.8}
    }

    # معاملات التصحيح حسب مستوى النشاط
    activity_factors = {
        'low': {'dm': 1.0, 'energy': 1.0},
        'moderate': {'dm': 1.05, 'energy': 1.1},
        'high': {'dm': 1.1, 'energy': 1.2}
    }

    stage_factor = stage_factors.get(stage, stage_factors['mid'])
    activity_factor = activity_factors.get(activity, activity_factors['moderate'])

    # حساب المادة الجافة (كغ/يوم)
    # معادلة NRC: DM = 0.372 * FCM + 0.0968 * BW^0.75
    metabolic_weight = weight ** 0.75
    fcm = milk * 1.05  # 4% fat corrected milk

    base_dm = (0.372 * fcm + 0.0968 * metabolic_weight) / 100 * weight
    base_dm = max(base_dm, weight * 0.025)  # الحد الأدنى 2.5% من الوزن

    total_dm = base_dm * stage_factor['dm'] * activity_factor['dm']

    # حساب البروتين (غرام/يوم)
    maintenance_protein = metabolic_weight * 3.8
    milk_protein = milk * 78  # 78 غرام بروتين لكل لتر
    total_protein = (maintenance_protein + milk_protein) * stage_factor['protein']

    # حساب الطاقة (Mcal/يوم)
    maintenance_energy = metabolic_weight * 0.08
    milk_energy = milk * 0.74  # 0.74 Mcal لكل لتر
    total_energy = (maintenance_energy + milk_energy) * stage_factor['energy'] * activity_factor['energy']

    # حساب نسبة البروتين
    protein_percentage = (total_protein / 1000) / total_dm * 100

    # حساب الألياف
    min_fiber = total_dm * 0.15  # 15% كحد أدنى
    max_fiber = total_dm * 0.30  # 30% كحد أقصى

    # توزيع العلف المقترح
    concentrate_feed = total_dm * 0.65  # 65% علف مركز
    roughage_feed = total_dm * 0.30     # 30% علف خشن
    supplement_feed = total_dm * 0.05   # 5% إضافات

    # حساب التكلفة التقديرية
    daily_cost = total_dm * 0.45  # متوسط تكلفة العلف

    return {
        'total_dm': round(total_dm, 2),
        'total_energy': round(total_energy, 2),
        'total_protein': round(total_protein, 0),
        'protein_percentage': round(protein_percentage, 1),
        'min_fiber': round(min_fiber, 2),
        'max_fiber': round(max_fiber, 2),
        'daily_cost': round(daily_cost, 2),
        'feed_distribution': {
            'concentrate': round(concentrate_feed, 2),
            'roughage': round(roughage_feed, 2),
            'supplement': round(supplement_feed, 2)
        },
        'animal_specific': {
            'weight': weight,
            'milk_production': milk,
            'lactation_stage': stage,
            'activity_level': activity
        }
    }

def calculate_calf_requirements(weight, age, growth, calf_type):
    """حساب احتياجات العجل"""
    # حساب احتياجات العجل
    maintenance_dm = weight * 0.025
    growth_dm = growth * 5  # 5 كغ مادة جافة لكل كغ نمو
    total_dm = maintenance_dm + growth_dm

    maintenance_energy = (weight ** 0.75) * 0.1
    growth_energy = growth * 4.5  # 4.5 Mcal لكل كغ نمو
    total_energy = maintenance_energy + growth_energy

    protein_for_growth = growth * 180  # 180 غرام بروتين لكل كغ نمو
    protein_for_maintenance = (weight ** 0.75) * 4
    total_protein = protein_for_growth + protein_for_maintenance

    daily_cost = total_dm * 0.42  # متوسط تكلفة العلف للعجول

    return {
        'total_dm': round(total_dm, 2),
        'total_energy': round(total_energy, 2),
        'total_protein': round(total_protein, 0),
        'protein_percentage': round((total_protein / 1000) / total_dm * 100, 1),
        'daily_cost': round(daily_cost, 2),
        'animal_specific': {
            'growth_rate': growth,
            'age_months': age,
            'type': calf_type
        }
    }

def calculate_sheep_requirements(weight, stage, lambs):
    """حساب احتياجات الخروف"""
    total_dm = weight * 0.03  # 3% من وزن الجسم
    total_energy = (weight ** 0.75) * 0.06
    total_protein = (weight ** 0.75) * 3

    # تعديل حسب المرحلة
    stage_multipliers = {
        'maintenance': {'dm': 1.0, 'energy': 1.0, 'protein': 1.0},
        'pregnant': {'dm': 1.2, 'energy': 1.3, 'protein': 1.4},
        'lactating': {'dm': 1.5, 'energy': 1.8, 'protein': 2.0},
        'growing': {'dm': 1.3, 'energy': 1.4, 'protein': 1.6}
    }

    multiplier = stage_multipliers.get(stage, stage_multipliers['maintenance'])
    total_dm *= multiplier['dm']
    total_energy *= multiplier['energy']
    total_protein *= multiplier['protein']

    # إضافة للمرضعة حسب عدد الحملان
    if stage == 'lactating' and lambs > 1:
        extra_multiplier = 1 + (lambs - 1) * 0.3
        total_dm *= extra_multiplier
        total_energy *= extra_multiplier
        total_protein *= extra_multiplier

    daily_cost = total_dm * 0.35  # تكلفة علف الأغنام

    return {
        'total_dm': round(total_dm, 2),
        'total_energy': round(total_energy, 2),
        'total_protein': round(total_protein, 0),
        'protein_percentage': round((total_protein / 1000) / total_dm * 100, 1),
        'daily_cost': round(daily_cost, 2),
        'animal_specific': {
            'stage': stage,
            'lambs_count': lambs
        }
    }

def calculate_goat_requirements(weight, milk, stage):
    """حساب احتياجات الماعز"""
    # حساب احتياجات الصيانة
    maintenance_dm = weight * 0.035  # الماعز يأكل أكثر نسبياً
    maintenance_energy = (weight ** 0.75) * 0.07
    maintenance_protein = (weight ** 0.75) * 3.5

    # إضافة احتياجات الحليب
    milk_dm = milk * 0.3
    milk_energy = milk * 0.65
    milk_protein = milk * 45

    total_dm = maintenance_dm + milk_dm
    total_energy = maintenance_energy + milk_energy
    total_protein = maintenance_protein + milk_protein

    # تعديل حسب المرحلة
    stage_factors = {
        'maintenance': 1.0,
        'pregnant': 1.25,
        'lactating': 1.1,  # مضاف مع الحليب
        'growing': 1.4
    }

    factor = stage_factors.get(stage, 1.0)
    if stage != 'lactating':  # لأن احتياجات الحليب مضافة بالفعل
        total_dm *= factor
        total_energy *= factor
        total_protein *= factor

    daily_cost = total_dm * 0.38  # تكلفة علف الماعز

    return {
        'total_dm': round(total_dm, 2),
        'total_energy': round(total_energy, 2),
        'total_protein': round(total_protein, 0),
        'protein_percentage': round((total_protein / 1000) / total_dm * 100, 1),
        'daily_cost': round(daily_cost, 2),
        'animal_specific': {
            'milk_production': milk,
            'stage': stage
        }
    }

def calculate_buffalo_requirements(weight, milk, stage, activity):
    """حساب احتياجات الجاموس"""
    # معاملات التصحيح حسب مرحلة الإنتاج
    stage_factors = {
        'early': {'dm': 1.1, 'protein': 1.15, 'energy': 1.1},
        'mid': {'dm': 1.0, 'protein': 1.0, 'energy': 1.0},
        'late': {'dm': 0.95, 'protein': 0.9, 'energy': 0.95},
        'dry': {'dm': 0.8, 'protein': 0.7, 'energy': 0.8}
    }

    # معاملات التصحيح حسب مستوى النشاط
    activity_factors = {
        'low': {'dm': 1.0, 'energy': 1.0},
        'moderate': {'dm': 1.05, 'energy': 1.1},
        'high': {'dm': 1.1, 'energy': 1.2}
    }

    stage_factor = stage_factors.get(stage, stage_factors['mid'])
    activity_factor = activity_factors.get(activity, activity_factors['moderate'])

    # حساب المادة الجافة
    maintenance_dm = weight * 0.026
    production_dm = milk * 0.37
    activity_dm = maintenance_dm * (activity_factor['dm'] - 1)
    total_dm = (maintenance_dm + production_dm + activity_dm) * stage_factor['dm']

    # حساب الطاقة
    maintenance_energy = (weight ** 0.75) * 0.08
    production_energy = milk * 0.74
    activity_energy = maintenance_energy * (activity_factor['energy'] - 1)
    total_energy = (maintenance_energy + production_energy + activity_energy) * stage_factor['energy']

    # حساب البروتين
    maintenance_protein = (weight ** 0.75) * 3.8
    production_protein = milk * 58
    total_protein = (maintenance_protein + production_protein) * stage_factor['protein']

    daily_cost = total_dm * 0.4

    return {
        'total_dm': round(total_dm, 2),
        'total_energy': round(total_energy, 2),
        'total_protein': round(total_protein, 0),
        'protein_percentage': round((total_protein / 1000) / total_dm * 100, 1),
        'daily_cost': round(daily_cost, 2),
        'animal_specific': {
            'milk_production': milk,
            'stage': stage
        }
    }

@app.route('/suggest_mix_for_cow', methods=['POST'])
def suggest_mix_for_cow():
    """اقتراح خلطة مناسبة لاحتياجات البقرة"""
    try:
        data = request.get_json()
        requirements = data.get('requirements')

        if not requirements:
            return jsonify({'error': 'لم يتم تمرير البيانات المطلوبة'}), 400

        # الحصول على جميع المكونات المتاحة
        ingredients = Ingredient.query.all()

        if not ingredients:
            return jsonify({'error': 'لا توجد مكونات متاحة في قاعدة البيانات'}), 400

        # تطبيق خوارزمية الخلط الأمثل
        suggested_mix = optimize_mix_for_requirements(requirements, ingredients)

        return jsonify({
            'success': True,
            'suggested_mix': suggested_mix
        })

    except Exception as e:
        return jsonify({'error': f'خطأ في اقتراح الخلطة: {str(e)}'}), 500

def optimize_mix_for_requirements(requirements, ingredients):
    """خوارزمية تحسين الخلطة حسب احتياجات البقرة"""

    target_dm = requirements['total_dm']
    target_protein = requirements['total_protein'] / 1000  # تحويل إلى كغ
    target_energy = requirements['total_energy']
    target_protein_percent = requirements['protein_percentage']

    # تحويل المكونات إلى قاموس للسهولة
    ingredients_list = []
    for ingredient in ingredients:
        ingredients_list.append({
            'id': ingredient.id,
            'name': ingredient.name,
            'protein': ingredient.protein,
            'energy': ingredient.energy,
            'fiber': ingredient.fiber,
            'price_per_kg': ingredient.price_per_kg
        })

    # تصنيف المكونات
    roughage_ingredients = []     # علف خشن
    protein_ingredients = []      # مصادر بروتين
    energy_ingredients = []       # مصادر طاقة
    supplement_ingredients = []   # إضافات

    for ingredient in ingredients_list:
        name_lower = ingredient['name'].lower()

        # تصنيف العلف الخشن
        if (ingredient['fiber'] >= 20 or
            any(word in name_lower for word in ['تبن', 'قش', 'دريس', 'برسيم', 'سيلاج', 'علف أخضر'])):
            roughage_ingredients.append(ingredient)
        # تصنيف مصادر البروتين
        elif ingredient['protein'] >= 25:
            protein_ingredients.append(ingredient)
        # تصنيف مصادر الطاقة
        elif ingredient['energy'] >= 2500:
            energy_ingredients.append(ingredient)
        # الباقي إضافات
        else:
            supplement_ingredients.append(ingredient)

    # اختيار أفضل المكونات
    mix_components = []

    # 1. اختيار العلف الخشن (30% من المادة الجافة)
    roughage_amount = target_dm * 0.30
    if roughage_ingredients:
        # اختيار أفضل علف خشن (أعلى بروتين مقابل السعر)
        best_roughage = max(roughage_ingredients,
                           key=lambda x: x['protein'] / max(x['price_per_kg'], 0.1))
        mix_components.append({
            'ingredient': best_roughage,
            'quantity': round(roughage_amount, 2),
            'percentage': 30.0,
            'reason': 'مصدر الألياف الأساسي'
        })

    # 2. اختيار مصدر البروتين (18% من المادة الجافة)
    protein_amount = target_dm * 0.18
    if protein_ingredients:
        # اختيار أفضل مصدر بروتين
        best_protein = max(protein_ingredients,
                          key=lambda x: x['protein'] / max(x['price_per_kg'], 0.1))
        mix_components.append({
            'ingredient': best_protein,
            'quantity': round(protein_amount, 2),
            'percentage': 18.0,
            'reason': 'مصدر البروتين الرئيسي'
        })

    # 3. اختيار مصدر الطاقة (47% من المادة الجافة)
    energy_amount = target_dm * 0.47
    if energy_ingredients:
        # اختيار أفضل مصدر طاقة
        best_energy = max(energy_ingredients,
                         key=lambda x: x['energy'] / max(x['price_per_kg'], 0.1))
        mix_components.append({
            'ingredient': best_energy,
            'quantity': round(energy_amount, 2),
            'percentage': 47.0,
            'reason': 'مصدر الطاقة الأساسي'
        })

    # 4. إضافة المكملات (5% من المادة الجافة)
    supplement_amount = target_dm * 0.05
    if supplement_ingredients:
        best_supplement = min(supplement_ingredients,
                             key=lambda x: x['price_per_kg'])
        mix_components.append({
            'ingredient': best_supplement,
            'quantity': round(supplement_amount, 2),
            'percentage': 5.0,
            'reason': 'مكملات غذائية'
        })

    # حساب الإجماليات
    total_weight = sum(comp['quantity'] for comp in mix_components)
    total_cost = sum(comp['quantity'] * comp['ingredient']['price_per_kg'] for comp in mix_components)

    # حساب مؤشرات الجودة
    total_protein_provided = sum(comp['quantity'] * comp['ingredient']['protein'] / 100 for comp in mix_components)
    total_energy_provided = sum(comp['quantity'] * comp['ingredient']['energy'] / 1000 for comp in mix_components)

    protein_match = min((total_protein_provided / target_protein) * 100, 100) if target_protein > 0 else 100
    energy_match = min((total_energy_provided / target_energy) * 100, 100) if target_energy > 0 else 100

    # توليد التوصيات
    recommendations = []
    if protein_match >= 95:
        recommendations.append({
            'type': 'good',
            'message': 'مستوى البروتين ممتاز',
            'suggestion': 'الخلطة تلبي احتياجات البروتين بشكل مثالي'
        })
    elif protein_match >= 85:
        recommendations.append({
            'type': 'warning',
            'message': 'مستوى البروتين جيد',
            'suggestion': 'يمكن زيادة مصادر البروتين قليلاً'
        })
    else:
        recommendations.append({
            'type': 'danger',
            'message': 'نقص في البروتين',
            'suggestion': 'يجب زيادة كمية مصادر البروتين'
        })

    if energy_match >= 95:
        recommendations.append({
            'type': 'good',
            'message': 'مستوى الطاقة ممتاز',
            'suggestion': 'الخلطة تلبي احتياجات الطاقة بشكل مثالي'
        })
    elif energy_match >= 85:
        recommendations.append({
            'type': 'warning',
            'message': 'مستوى الطاقة جيد',
            'suggestion': 'يمكن زيادة مصادر الطاقة قليلاً'
        })
    else:
        recommendations.append({
            'type': 'danger',
            'message': 'نقص في الطاقة',
            'suggestion': 'يجب زيادة كمية مصادر الطاقة'
        })

    # تقييم كفاءة التكلفة
    cost_per_kg = total_cost / total_weight if total_weight > 0 else 0
    if cost_per_kg <= 0.5:
        cost_efficiency = 'ممتاز'
        recommendations.append({
            'type': 'good',
            'message': 'التكلفة اقتصادية جداً',
            'suggestion': 'خلطة ذات كفاءة عالية في التكلفة'
        })
    elif cost_per_kg <= 0.7:
        cost_efficiency = 'جيد'
        recommendations.append({
            'type': 'info',
            'message': 'التكلفة معقولة',
            'suggestion': 'خلطة متوازنة في التكلفة والجودة'
        })
    else:
        cost_efficiency = 'مرتفع'
        recommendations.append({
            'type': 'warning',
            'message': 'التكلفة مرتفعة',
            'suggestion': 'يمكن البحث عن بدائل أقل تكلفة'
        })

    return {
        'mix_components': mix_components,
        'totals': {
            'weight': round(total_weight, 2),
            'cost': round(total_cost, 2),
            'cost_per_kg': round(cost_per_kg, 3)
        },
        'quality_indicators': {
            'protein_match': round(protein_match, 1),
            'energy_match': round(energy_match, 1),
            'cost_efficiency': cost_efficiency
        },
        'recommendations': recommendations
    }

@app.route('/compare_cows')
def compare_cows():
    """صفحة مقارنة الأبقار"""
    calculations = CowCalculation.query.order_by(CowCalculation.calculation_date.desc()).all()
    return render_template('compare_cows_simple.html', calculations=calculations)





@app.route('/')
def index():
    """الصفحة الرئيسية"""
    ingredients = Ingredient.query.all()
    return render_template('index.html', ingredients=ingredients)

@app.route('/cow_requirements')
def cow_requirements():
    """صفحة حساب احتياجات البقرة"""
    # Get data from query parameters for recalculation
    weight = request.args.get('weight', '')
    milk = request.args.get('milk', '')
    stage = request.args.get('stage', 'mid')
    activity = request.args.get('activity', 'moderate')
    
    return render_template('cow_requirements.html', 
                           weight=weight, 
                           milk=milk, 
                           stage=stage, 
                           activity=activity)

@app.route('/add_ingredient', methods=['POST'])
def add_ingredient():
    """إضافة مكون جديد"""
    try:
        name = request.form['name']
        protein = float(request.form['protein'])
        energy = float(request.form['energy'])
        fiber = float(request.form['fiber'])
        price_per_kg = float(request.form['price_per_kg'])
        
        new_ingredient = Ingredient(
            name=name,
            protein=protein,
            energy=energy,
            fiber=fiber,
            price_per_kg=price_per_kg
        )
        
        db.session.add(new_ingredient)
        db.session.commit()
        
        flash('تم إضافة المكون بنجاح!', 'success')
    except Exception as e:
        flash(f'خطأ في إضافة المكون: {str(e)}', 'error')
    
    return redirect(url_for('index'))

@app.route('/calculate_mix', methods=['POST'])
def calculate_mix():
    """حساب خصائص الخلطة"""
    try:
        data = request.get_json()
        ingredients_data = data['ingredients']
        
        total_weight = 0
        total_cost = 0
        total_protein_weight = 0
        total_energy_weight = 0
        total_fiber_weight = 0
        
        results = []
        
        for item in ingredients_data:
            ingredient_id = item['id']
            quantity = float(item['quantity'])
            
            if quantity <= 0:
                continue
                
            ingredient = Ingredient.query.get(ingredient_id)
            if not ingredient:
                continue
            
            # حساب التكلفة والقيم الغذائية
            cost = quantity * ingredient.price_per_kg
            protein_contribution = (quantity * ingredient.protein) / 100
            energy_contribution = quantity * ingredient.energy
            fiber_contribution = (quantity * ingredient.fiber) / 100
            
            total_weight += quantity
            total_cost += cost
            total_protein_weight += protein_contribution
            total_energy_weight += energy_contribution
            total_fiber_weight += fiber_contribution
            
            results.append({
                'name': ingredient.name,
                'quantity': quantity,
                'cost': round(cost, 3),
                'protein_contribution': round(protein_contribution, 2),
                'energy_contribution': round(energy_contribution, 2),
                'fiber_contribution': round(fiber_contribution, 2)
            })
        
        if total_weight == 0:
            return jsonify({'error': 'لا توجد كميات محددة'})
        
        # حساب المتوسطات
        avg_protein = (total_protein_weight / total_weight) * 100
        avg_energy = total_energy_weight / total_weight
        avg_fiber = (total_fiber_weight / total_weight) * 100
        
        return jsonify({
            'success': True,
            'total_weight': round(total_weight, 2),
            'total_cost': round(total_cost, 3),
            'avg_protein': round(avg_protein, 2),
            'avg_energy': round(avg_energy, 2),
            'avg_fiber': round(avg_fiber, 2),
            'cost_per_kg': round(total_cost / total_weight, 3),
            'details': results
        })
        
    except Exception as e:
        return jsonify({'error': f'خطأ في الحساب: {str(e)}'})

@app.route('/save_mix', methods=['POST'])
def save_mix():
    """حفظ الخلطة"""
    try:
        data = request.get_json()
        mix_name = data['name']
        ingredients_data = data['ingredients']
        calculations = data['calculations']
        
        # إنشاء خلطة جديدة
        new_mix = Mix(
            name=mix_name,
            total_weight=calculations['total_weight'],
            total_cost=calculations['total_cost'],
            avg_protein=calculations['avg_protein'],
            avg_energy=calculations['avg_energy'],
            avg_fiber=calculations['avg_fiber']
        )
        
        db.session.add(new_mix)
        db.session.flush()  # للحصول على ID الخلطة
        
        # إضافة تفاصيل الخلطة
        for item in ingredients_data:
            if float(item['quantity']) > 0:
                detail = MixDetail(
                    mix_id=new_mix.id,
                    ingredient_id=item['id'],
                    quantity_kg=float(item['quantity'])
                )
                db.session.add(detail)
        
        db.session.commit()
        
        return jsonify({'success': True, 'message': 'تم حفظ الخلطة بنجاح!'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'خطأ في حفظ الخلطة: {str(e)}'})

@app.route('/saved_mixes')
def saved_mixes():
    """عرض الخلطات المحفوظة"""
    mixes = Mix.query.order_by(Mix.date_created.desc()).all()
    return render_template('saved_mixes.html', mixes=mixes)

@app.route('/view_mix/<int:mix_id>')
def view_mix(mix_id):
    """عرض تفاصيل خلطة محددة"""
    mix = Mix.query.get_or_404(mix_id)
    return render_template('view_mix.html', mix=mix)

@app.route('/delete_mix/<int:mix_id>', methods=['POST'])
def delete_mix(mix_id):
    """حذف خلطة"""
    try:
        mix = Mix.query.get_or_404(mix_id)
        db.session.delete(mix)
        db.session.commit()
        flash('تم حذف الخلطة بنجاح!', 'success')
    except Exception as e:
        flash(f'خطأ في حذف الخلطة: {str(e)}', 'error')
    
    return redirect(url_for('saved_mixes'))

@app.route('/api/ingredients')
def api_ingredients():
    """API لجلب جميع المكونات"""
    ingredients = Ingredient.query.all()
    return jsonify([ingredient.to_dict() for ingredient in ingredients])



@app.route('/save_cow_calculation', methods=['POST'])
def save_cow_calculation():
    """API لحفظ حسابات البقرة"""
    try:
        data = request.get_json()
        
        new_calculation = CowCalculation(
            cow_name=data['cow_name'],
            weight=data['weight'],
            milk_production=data['milk_production'],
            lactation_stage=data['lactation_stage'],
            activity_level=data['activity_level'],
            total_dm=data['requirements']['totalDM'],
            total_protein=data['requirements']['totalProtein'],
            total_energy=data['requirements']['totalEnergy'],
            daily_cost=data['requirements']['dailyCost']
        )
        
        db.session.add(new_calculation)
        db.session.commit()
        
        return jsonify({'message': 'تم حفظ حسابات البقرة بنجاح!'})
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/cow_calculation/<int:calc_id>')
def get_cow_calculation(calc_id):
    """API لجلب تفاصيل حساب بقرة"""
    try:
        calc = CowCalculation.query.get_or_404(calc_id)
        return jsonify({
            'id': calc.id,
            'cow_name': calc.cow_name,
            'weight': calc.weight,
            'milk_production': calc.milk_production,
            'lactation_stage': calc.lactation_stage,
            'activity_level': calc.activity_level,
            'calculation_date': calc.calculation_date.isoformat() if calc.calculation_date else None,
            'total_dm': calc.total_dm,
            'total_protein': calc.total_protein,
            'total_energy': calc.total_energy,
            'daily_cost': calc.daily_cost
        })
    except Exception as e:
        return jsonify({'error': f'خطأ في جلب البيانات: {str(e)}'}), 500

@app.route('/delete_cow_calculation/<int:calc_id>', methods=['POST'])
def delete_cow_calculation(calc_id):
    """API لحذف حساب بقرة"""
    try:
        calc = CowCalculation.query.get_or_404(calc_id)
        cow_name = calc.cow_name
        
        db.session.delete(calc)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'تم حذف سجل البقرة "{cow_name}" بنجاح'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'خطأ في حذف السجل: {str(e)}'
        })

@app.route('/recalculate_cow/<int:calc_id>')
def recalculate_cow(calc_id):
    """إعادة حساب بقرة"""
    try:
        calc = CowCalculation.query.get_or_404(calc_id)
        # Redirect to cow_requirements page with query parameters
        return redirect(url_for('cow_requirements', 
                                weight=calc.weight, 
                                milk=calc.milk_production, 
                                stage=calc.lactation_stage, 
                                activity=calc.activity_level,
                                cow_name=calc.cow_name))
    except Exception as e:
        flash(f'خطأ في إعادة الحساب: {str(e)}', 'error')
        return redirect(url_for('cow_history'))

@app.route('/cow_history')
def cow_history():
    """صفحة تاريخ حسابات الأبقار"""
    calculations = CowCalculation.query.order_by(CowCalculation.calculation_date.desc()).all()
    return render_template('cow_history.html', calculations=calculations)

@app.route('/manage_ingredients')
def manage_ingredients():
    """صفحة إدارة المكونات"""
    ingredients = Ingredient.query.all()
    return render_template('manage_ingredients.html', ingredients=ingredients)

@app.route('/edit_ingredient/<int:ingredient_id>', methods=['POST'])
def edit_ingredient(ingredient_id):
    """API لتعديل مكون"""
    try:
        ingredient = Ingredient.query.get_or_404(ingredient_id)
        ingredient.name = request.form['name']
        ingredient.protein = float(request.form['protein'])
        ingredient.energy = float(request.form['energy'])
        ingredient.fiber = float(request.form['fiber'])
        ingredient.price_per_kg = float(request.form['price_per_kg'])
        db.session.commit()
        flash('تم تعديل المكون بنجاح!', 'success')
    except Exception as e:
        flash(f'خطأ في تعديل المكون: {str(e)}', 'error')
    return redirect(url_for('manage_ingredients'))

@app.route('/delete_ingredient/<int:ingredient_id>', methods=['POST'])
def delete_ingredient(ingredient_id):
    """API لحذف مكون"""
    try:
        ingredient = Ingredient.query.get_or_404(ingredient_id)
        db.session.delete(ingredient)
        db.session.commit()
        flash('تم حذف المكون بنجاح!', 'success')
    except Exception as e:
        flash(f'خطأ في حذف المكون: {str(e)}', 'error')
    return redirect(url_for('manage_ingredients'))

# إضافة routes اقتراح الخلطة
from mix_suggestion_route import setup_mix_routes
setup_mix_routes(app)





if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)